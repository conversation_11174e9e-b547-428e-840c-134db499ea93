#!/usr/bin/env python3
"""
OpenAI-Compatible API Wrapper for Code Morningstar
Makes Code Morningstar compatible with Jan.ai and other OpenAI-compatible clients
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional, Dict, Any, Union
import time
import uuid
from datetime import datetime
import asyncio
from services.llm_service import LLMService
from settings import get_settings
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Code Morningstar OpenAI-Compatible API",
    description="OpenAI-compatible API wrapper for Code Morningstar",
    version="1.0.0"
)

# Add CORS middleware for Jan.ai compatibility
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize settings and LLM service
settings = get_settings()
llm_service = LLMService(settings.llm_model_path)

# OpenAI-compatible models
class ChatCompletionMessage(BaseModel):
    role: str  # "system", "user", "assistant"
    content: str
    name: Optional[str] = None

class ChatCompletionRequest(BaseModel):
    model: str = "code-morningstar-34b"
    messages: List[ChatCompletionMessage]
    temperature: Optional[float] = 0.1
    max_tokens: Optional[int] = 4096
    top_p: Optional[float] = 0.95
    frequency_penalty: Optional[float] = 0.0
    presence_penalty: Optional[float] = 0.0
    stop: Optional[Union[str, List[str]]] = None
    stream: Optional[bool] = False

class ChatCompletionChoice(BaseModel):
    index: int
    message: ChatCompletionMessage
    finish_reason: str

class ChatCompletionUsage(BaseModel):
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int

class ChatCompletionResponse(BaseModel):
    id: str
    object: str = "chat.completion"
    created: int
    model: str
    choices: List[ChatCompletionChoice]
    usage: ChatCompletionUsage

class ModelInfo(BaseModel):
    id: str
    object: str = "model"
    created: int
    owned_by: str = "code-morningstar"

class ModelsResponse(BaseModel):
    object: str = "list"
    data: List[ModelInfo]

@app.get("/v1/models")
async def list_models():
    """List available models (OpenAI-compatible)"""
    return ModelsResponse(
        data=[
            ModelInfo(
                id="code-morningstar-34b",
                created=int(time.time()),
                owned_by="code-morningstar"
            )
        ]
    )

@app.post("/v1/chat/completions")
async def create_chat_completion(request: ChatCompletionRequest):
    """Create chat completion (OpenAI-compatible)"""
    try:
        # Convert messages to a single prompt
        prompt = convert_messages_to_prompt(request.messages)
        
        # Generate response using Code Morningstar
        start_time = time.time()
        response_text = await llm_service.generate_async(
            prompt=prompt,
            max_tokens=request.max_tokens or 4096,
            temperature=request.temperature or 0.1
        )
        
        # Calculate usage
        prompt_tokens = estimate_tokens(prompt)
        completion_tokens = estimate_tokens(response_text)
        
        # Create OpenAI-compatible response
        completion_id = f"chatcmpl-{uuid.uuid4().hex[:29]}"
        
        return ChatCompletionResponse(
            id=completion_id,
            created=int(start_time),
            model=request.model,
            choices=[
                ChatCompletionChoice(
                    index=0,
                    message=ChatCompletionMessage(
                        role="assistant",
                        content=response_text
                    ),
                    finish_reason="stop"
                )
            ],
            usage=ChatCompletionUsage(
                prompt_tokens=prompt_tokens,
                completion_tokens=completion_tokens,
                total_tokens=prompt_tokens + completion_tokens
            )
        )
        
    except Exception as e:
        logger.error(f"Chat completion error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

def convert_messages_to_prompt(messages: List[ChatCompletionMessage]) -> str:
    """Convert OpenAI chat messages to Code Morningstar prompt format"""
    
    # System message for coding expertise
    system_prompt = """You are Code Morningstar, an expert AI coding assistant with deep knowledge of:
- Software architecture and design patterns
- Performance optimization and best practices  
- Security vulnerabilities and mitigation
- Multiple programming languages and frameworks
- Algorithm design and data structures

Provide detailed, professional-quality responses with code examples when appropriate."""
    
    # Build conversation
    conversation_parts = []
    
    for message in messages:
        if message.role == "system":
            system_prompt = message.content
        elif message.role == "user":
            conversation_parts.append(f"Human: {message.content}")
        elif message.role == "assistant":
            conversation_parts.append(f"Assistant: {message.content}")
    
    # Combine into final prompt
    if conversation_parts:
        conversation = "\n\n".join(conversation_parts)
        prompt = f"""<s>[INST] <<SYS>>
{system_prompt}
<</SYS>>

{conversation} [/INST]"""
    else:
        prompt = f"""<s>[INST] <<SYS>>
{system_prompt}
<</SYS>>

Hello! How can I help you with coding today? [/INST]"""
    
    return prompt

def estimate_tokens(text: str) -> int:
    """Rough token estimation (1 token ≈ 4 characters)"""
    return max(1, len(text) // 4)

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "model_loaded": llm_service.is_model_loaded(),
        "api_version": "OpenAI-compatible v1.0.0",
        "backend": "Code Morningstar v2.0"
    }

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "🌟 Code Morningstar OpenAI-Compatible API",
        "version": "1.0.0",
        "description": "Use with Jan.ai or any OpenAI-compatible client",
        "endpoints": {
            "chat": "/v1/chat/completions",
            "models": "/v1/models",
            "health": "/health"
        },
        "jan_ai_setup": {
            "base_url": "http://localhost:8001",
            "model_id": "code-morningstar-34b",
            "api_key": "not-required"
        }
    }

if __name__ == "__main__":
    import uvicorn
    
    logger.info("🌟 Starting Code Morningstar OpenAI-Compatible API...")
    logger.info("📡 This API is compatible with Jan.ai and other OpenAI clients")
    logger.info("🔗 Jan.ai setup: http://localhost:8001")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=1337,  # Jan.ai compatible port
        log_level="info"
    )
