<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Code Morningstar - Windows UI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f3f3f3;
            color: #000;
            overflow-x: hidden;
        }

        /* Windows 11 Style Taskbar */
        .taskbar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 48px;
            background: rgba(32, 32, 32, 0.85);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .start-button {
            width: 48px;
            height: 32px;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 4px;
            color: white;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.2s;
            margin-right: 8px;
        }

        .start-button:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .taskbar-item {
            width: 40px;
            height: 32px;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 4px;
            color: white;
            cursor: pointer;
            margin: 0 2px;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .taskbar-item.active {
            background: rgba(255, 255, 255, 0.3);
            border-bottom: 2px solid #0078d4;
        }

        .taskbar-item:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        /* Main Window */
        .window {
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 68px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* Title Bar */
        .title-bar {
            height: 32px;
            background: #f0f0f0;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 8px;
            -webkit-app-region: drag;
        }

        .title {
            font-size: 14px;
            font-weight: 400;
            color: #323130;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .window-controls {
            display: flex;
            -webkit-app-region: no-drag;
        }

        .control-btn {
            width: 46px;
            height: 32px;
            border: none;
            background: transparent;
            cursor: pointer;
            font-size: 10px;
            color: #323130;
            transition: background 0.2s;
        }

        .control-btn:hover {
            background: rgba(0, 0, 0, 0.1);
        }

        .control-btn.close:hover {
            background: #e81123;
            color: white;
        }

        /* Content Area */
        .content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #fafafa;
        }

        /* Header Section */
        .header {
            background: linear-gradient(135deg, #0078d4 0%, #106ebe 100%);
            color: white;
            padding: 24px;
            text-align: center;
        }

        .header h1 {
            font-size: 32px;
            font-weight: 300;
            margin-bottom: 8px;
        }

        .header p {
            font-size: 16px;
            opacity: 0.9;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 16px;
            border-radius: 20px;
            margin-top: 16px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #00ff00;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Main Content */
        .main-content {
            flex: 1;
            padding: 24px;
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 24px;
        }

        .primary-panel {
            background: white;
            border-radius: 8px;
            border: 1px solid #e1e1e1;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
        }

        .panel-header {
            padding: 16px 20px;
            border-bottom: 1px solid #e1e1e1;
            background: #f8f9fa;
            border-radius: 8px 8px 0 0;
        }

        .panel-header h2 {
            font-size: 18px;
            font-weight: 600;
            color: #323130;
            margin: 0;
        }

        .panel-content {
            flex: 1;
            padding: 20px;
        }

        /* Input Section */
        .input-area {
            display: flex;
            flex-direction: column;
            gap: 16px;
            margin-bottom: 20px;
        }

        .fluent-textarea {
            width: 100%;
            min-height: 120px;
            padding: 12px;
            border: 2px solid #e1e1e1;
            border-radius: 4px;
            font-family: inherit;
            font-size: 14px;
            line-height: 1.5;
            resize: vertical;
            transition: border-color 0.2s;
        }

        .fluent-textarea:focus {
            outline: none;
            border-color: #0078d4;
        }

        .button-group {
            display: flex;
            gap: 12px;
        }

        .fluent-button {
            padding: 8px 20px;
            border: 1px solid #e1e1e1;
            border-radius: 4px;
            background: white;
            color: #323130;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .fluent-button:hover {
            background: #f3f2f1;
            border-color: #c7c7c7;
        }

        .fluent-button.primary {
            background: #0078d4;
            border-color: #0078d4;
            color: white;
        }

        .fluent-button.primary:hover {
            background: #106ebe;
        }

        .fluent-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        /* Response Section */
        .response-area {
            background: #f8f9fa;
            border: 1px solid #e1e1e1;
            border-radius: 4px;
            padding: 16px;
            min-height: 200px;
            font-family: 'Consolas', 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            white-space: pre-wrap;
            overflow-y: auto;
        }

        /* Sidebar */
        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .sidebar-panel {
            background: white;
            border: 1px solid #e1e1e1;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .sidebar-header {
            padding: 12px 16px;
            background: #f8f9fa;
            border-bottom: 1px solid #e1e1e1;
            border-radius: 8px 8px 0 0;
            font-weight: 600;
            font-size: 14px;
            color: #323130;
        }

        .sidebar-content {
            padding: 16px;
        }

        /* Settings Panel */
        .setting-item {
            margin-bottom: 16px;
        }

        .setting-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #323130;
            margin-bottom: 8px;
        }

        .fluent-slider {
            width: 100%;
            height: 4px;
            background: #e1e1e1;
            border-radius: 2px;
            outline: none;
            -webkit-appearance: none;
            appearance: none;
        }

        .fluent-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            background: #0078d4;
            border-radius: 50%;
            cursor: pointer;
        }

        /* Examples Panel */
        .example-grid {
            display: grid;
            gap: 8px;
        }

        .example-item {
            padding: 12px;
            background: #f8f9fa;
            border: 1px solid #e1e1e1;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .example-item:hover {
            background: #e1e1e1;
            border-color: #0078d4;
        }

        .example-title {
            font-weight: 600;
            color: #0078d4;
            font-size: 13px;
            margin-bottom: 4px;
        }

        .example-text {
            font-size: 12px;
            color: #605e5c;
            line-height: 1.4;
        }

        /* History Panel */
        .history-item {
            padding: 12px;
            border-bottom: 1px solid #e1e1e1;
            cursor: pointer;
            transition: background 0.2s;
        }

        .history-item:hover {
            background: #f8f9fa;
        }

        .history-item:last-child {
            border-bottom: none;
        }

        .history-prompt {
            font-size: 13px;
            font-weight: 600;
            color: #323130;
            margin-bottom: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .history-response {
            font-size: 12px;
            color: #605e5c;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .history-time {
            font-size: 11px;
            color: #8a8886;
            margin-top: 4px;
        }

        /* Loading Spinner */
        .loading-spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #e1e1e1;
            border-radius: 50%;
            border-top-color: #0078d4;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            
            .window {
                left: 10px;
                right: 10px;
                top: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="window">
        <div class="title-bar">
            <div class="title">
                <span>🌟</span>
                Code Morningstar - Local LLM Assistant
            </div>
            <div class="window-controls">
                <button class="control-btn">−</button>
                <button class="control-btn">□</button>
                <button class="control-btn close">✕</button>
            </div>
        </div>

        <div class="content">
            <div class="header">
                <h1>Code Morningstar</h1>
                <p>Your Local AI-Powered Assistant</p>
                <div class="status-badge">
                    <span class="status-dot"></span>
                    <span id="status-text">Checking status...</span>
                </div>
            </div>

            <div class="main-content">
                <div class="primary-panel">
                    <div class="panel-header">
                        <h2>💬 Conversation</h2>
                    </div>
                    <div class="panel-content">
                        <div class="input-area">
                            <textarea 
                                id="prompt-input" 
                                class="fluent-textarea" 
                                placeholder="Enter your prompt here... (Ctrl+Enter to send)"
                                rows="5"
                            ></textarea>
                            <div class="button-group">
                                <button id="send-btn" class="fluent-button primary">
                                    <span>🚀</span>
                                    Generate
                                </button>
                                <button id="clear-btn" class="fluent-button">
                                    <span>🗑️</span>
                                    Clear
                                </button>
                                <button id="copy-btn" class="fluent-button">
                                    <span>📋</span>
                                    Copy Response
                                </button>
                            </div>
                        </div>
                        
                        <div id="response-area" class="response-area">
Your AI response will appear here...
                        </div>
                    </div>
                </div>

                <div class="sidebar">
                    <div class="sidebar-panel">
                        <div class="sidebar-header">⚙️ Advanced Settings</div>
                        <div class="sidebar-content">
                            <div class="setting-item">
                                <label class="setting-label">Max Tokens: <span id="tokens-value">2048</span></label>
                                <input type="range" id="max-tokens" class="fluent-slider" min="1" max="8192" value="2048">
                            </div>
                            <div class="setting-item">
                                <label class="setting-label">Temperature: <span id="temp-value">0.7</span></label>
                                <input type="range" id="temperature" class="fluent-slider" min="0" max="2" step="0.1" value="0.7">
                            </div>
                            <div class="setting-item">
                                <label class="setting-label">Top P: <span id="top-p-value">0.9</span></label>
                                <input type="range" id="top-p" class="fluent-slider" min="0" max="1" step="0.01" value="0.9">
                            </div>
                            <div class="setting-item">
                                <label class="setting-label">Top K: <span id="top-k-value">40</span></label>
                                <input type="range" id="top-k" class="fluent-slider" min="1" max="100" value="40">
                            </div>
                            <div class="setting-item">
                                <label class="setting-label">Repeat Penalty: <span id="repeat-penalty-value">1.1</span></label>
                                <input type="range" id="repeat-penalty" class="fluent-slider" min="0" max="2" step="0.1" value="1.1">
                            </div>
                            <div class="setting-item">
                                <label class="setting-label">Frequency Penalty: <span id="freq-penalty-value">0.0</span></label>
                                <input type="range" id="frequency-penalty" class="fluent-slider" min="-2" max="2" step="0.1" value="0">
                            </div>
                            <div class="setting-item">
                                <label class="setting-label">Presence Penalty: <span id="presence-penalty-value">0.0</span></label>
                                <input type="range" id="presence-penalty" class="fluent-slider" min="-2" max="2" step="0.1" value="0">
                            </div>
                            <div class="setting-item">
                                <button id="reset-defaults" class="fluent-button" style="width: 100%; margin-top: 8px;">
                                    🔄 Reset to Defaults
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="sidebar-panel">
                        <div class="sidebar-header">💡 Examples</div>
                        <div class="sidebar-content">
                            <div class="example-grid" id="examples-container">
                                <!-- Examples will be populated here -->
                            </div>
                        </div>
                    </div>

                    <div class="sidebar-panel">
                        <div class="sidebar-header">📜 History</div>
                        <div class="sidebar-content">
                            <div id="history-container">
                                <p style="color: #8a8886; font-size: 12px; text-align: center;">No conversations yet</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="taskbar">
        <button class="start-button">⊞</button>
        <button class="taskbar-item active">🌟</button>
        <button class="taskbar-item">📁</button>
        <button class="taskbar-item">⚙️</button>
    </div>

    <script>
        // Example prompts
        const examples = [
            {
                title: "Python Function",
                prompt: "Write a Python function to calculate the factorial of a number using recursion."
            },
            {
                title: "JavaScript API",
                prompt: "Create a JavaScript function that fetches data from a REST API and handles errors."
            },
            {
                title: "SQL Query",
                prompt: "Write a SQL query to find the top 5 customers by total purchase amount."
            },
            {
                title: "React Component",
                prompt: "Create a React component for a simple todo list with add and delete functionality."
            },
            {
                title: "CSS Animation",
                prompt: "Write CSS code for a smooth fade-in animation that triggers on page load."
            },
            {
                title: "REST API Endpoint",
                prompt: "Create a Flask REST API endpoint that handles user authentication with JWT tokens."
            },
            {
                title: "Database Schema",
                prompt: "Design a SQL database schema for an e-commerce platform with products, orders, and customers."
            },
            {
                title: "Algorithm Implementation",
                prompt: "Implement a binary search algorithm in Python with detailed comments explaining each step."
            }
        ];

        let conversationHistory = [];
        let isLoading = false;
        let defaultParameters = {};

        // DOM elements
        const promptInput = document.getElementById('prompt-input');
        const responseArea = document.getElementById('response-area');
        const sendBtn = document.getElementById('send-btn');
        const clearBtn = document.getElementById('clear-btn');
        const copyBtn = document.getElementById('copy-btn');
        const statusText = document.getElementById('status-text');
        const examplesContainer = document.getElementById('examples-container');
        const historyContainer = document.getElementById('history-container');
        const resetBtn = document.getElementById('reset-defaults');

        // Slider elements and their value displays
        const sliders = {
            'max-tokens': { element: null, valueDisplay: null },
            'temperature': { element: null, valueDisplay: null },
            'top-p': { element: null, valueDisplay: null },
            'top-k': { element: null, valueDisplay: null },
            'repeat-penalty': { element: null, valueDisplay: null },
            'frequency-penalty': { element: null, valueDisplay: null },
            'presence-penalty': { element: null, valueDisplay: null }
        };

        // Initialize DOM references
        function initializeSliderReferences() {
            Object.keys(sliders).forEach(sliderId => {
                sliders[sliderId].element = document.getElementById(sliderId);
                sliders[sliderId].valueDisplay = document.getElementById(sliderId.replace('-', '-') + '-value');
            });
        }

        // Initialize
        async function initialize() {
            initializeSliderReferences();
            await loadDefaultParameters();
            await checkModelStatus();
            populateExamples();
            setupEventListeners();
        }

        // Load default parameters from backend
        async function loadDefaultParameters() {
            try {
                const response = await fetch('http://localhost:8000/llm/parameters');
                defaultParameters = await response.json();
                console.log('Default parameters loaded:', defaultParameters);
            } catch (error) {
                console.error('Failed to load default parameters:', error);
                // Fallback defaults
                defaultParameters = {
                    temperature: {default: 0.7},
                    max_tokens: {default: 2048},
                    top_p: {default: 0.9},
                    top_k: {default: 40},
                    repeat_penalty: {default: 1.1},
                    frequency_penalty: {default: 0.0},
                    presence_penalty: {default: 0.0}
                };
            }
        }

        // Reset sliders to default values
        function resetToDefaults() {
            Object.keys(sliders).forEach(sliderId => {
                const paramName = sliderId.replace('-', '_');
                const defaultValue = defaultParameters[paramName]?.default;
                if (defaultValue !== undefined && sliders[sliderId].element) {
                    sliders[sliderId].element.value = defaultValue;
                    updateSliderValue(sliderId);
                }
            });
        }

        // Update slider value display
        function updateSliderValue(sliderId) {
            const slider = sliders[sliderId];
            if (slider.element && slider.valueDisplay) {
                const value = parseFloat(slider.element.value);
                slider.valueDisplay.textContent = value % 1 === 0 ? value.toString() : value.toFixed(2);
            }
        }

        // Check model status
        async function checkModelStatus() {
            try {
                const response = await fetch('http://localhost:8000/llm/health');
                const data = await response.json();
                
                if (data.status === 'healthy') {
                    if (data.model_loaded) {
                        statusText.textContent = 'Model Loaded - Ready';
                        document.querySelector('.status-dot').style.background = '#00ff00';
                    } else {
                        statusText.textContent = 'Model Not Loaded';
                        document.querySelector('.status-dot').style.background = '#ff0000';
                    }
                } else {
                    statusText.textContent = 'Service Issue';
                    document.querySelector('.status-dot').style.background = '#ff8800';
                }
            } catch (error) {
                statusText.textContent = 'Connection Error';
                document.querySelector('.status-dot').style.background = '#ff0000';
            }
        }

        // Populate example prompts
        function populateExamples() {
            examplesContainer.innerHTML = examples.map(example => `
                <div class="example-item" onclick="loadExample('${example.prompt.replace(/'/g, "\\'")}')">
                    <div class="example-title">${example.title}</div>
                    <div class="example-text">${example.prompt.substring(0, 60)}...</div>
                </div>
            `).join('');
        }

        // Load example into input
        function loadExample(prompt) {
            promptInput.value = prompt;
            promptInput.focus();
        }

        // Setup event listeners
        function setupEventListeners() {
            sendBtn.addEventListener('click', sendPrompt);
            clearBtn.addEventListener('click', clearInput);
            copyBtn.addEventListener('click', copyResponse);
            resetBtn.addEventListener('click', resetToDefaults);
            
            promptInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                    e.preventDefault();
                    sendPrompt();
                }
            });

            // Setup all slider event listeners
            Object.keys(sliders).forEach(sliderId => {
                const slider = sliders[sliderId].element;
                if (slider) {
                    slider.addEventListener('input', () => updateSliderValue(sliderId));
                    // Initialize display
                    updateSliderValue(sliderId);
                }
            });
        }

        // Get current parameter values
        function getCurrentParameters() {
            const params = {
                prompt: promptInput.value.trim()
            };

            Object.keys(sliders).forEach(sliderId => {
                const slider = sliders[sliderId].element;
                if (slider) {
                    const paramName = sliderId.replace('-', '_');
                    let value = slider.value;
                    
                    // Convert to appropriate type
                    if (sliderId === 'max-tokens' || sliderId === 'top-k') {
                        params[paramName] = parseInt(value);
                    } else {
                        params[paramName] = parseFloat(value);
                    }
                }
            });

            return params;
        }

        // Send prompt to API
        async function sendPrompt() {
            if (!promptInput.value.trim() || isLoading) return;

            const params = getCurrentParameters();
            
            isLoading = true;
            updateLoadingState(true);

            try {
                console.log('Sending request with parameters:', params);
                
                const response = await fetch('http://localhost:8000/llm/generate', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(params)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();
                console.log('Received response:', data);
                
                const result = data.result || data.response || data.detail || "No response received";
                
                // Display result with formatting
                responseArea.innerHTML = `
                    <div style="margin-bottom: 16px;">
                        <strong>Generated Code:</strong>
                    </div>
                    <pre style="background: #f8f8f8; padding: 16px; border-radius: 4px; overflow-x: auto; white-space: pre-wrap;">${result}</pre>
                    <div style="margin-top: 16px; padding: 12px; background: #e8f4f8; border-radius: 4px; font-size: 12px;">
                        <strong>Generation Stats:</strong><br>
                        • Tokens: ${data.tokens_used || 'N/A'}<br>
                        • Time: ${data.processing_time ? (data.processing_time * 1000).toFixed(0) + 'ms' : 'N/A'}<br>
                        • Temperature: ${params.temperature}<br>
                        • Max Tokens: ${params.max_tokens}
                    </div>
                `;
                
                // Add to history
                conversationHistory.unshift({
                    prompt: params.prompt,
                    response: result,
                    timestamp: new Date(),
                    parameters: params,
                    stats: {
                        tokens: data.tokens_used,
                        time: data.processing_time
                    }
                });
                
                // Keep only last 10 conversations
                if (conversationHistory.length > 10) {
                    conversationHistory = conversationHistory.slice(0, 10);
                }
                
                updateHistoryDisplay();
                clearInput();

            } catch (error) {
                console.error('Generation error:', error);
                responseArea.innerHTML = `<div style="color: #d13438; padding: 16px; background: #fef7f7; border-radius: 4px;"><strong>Error:</strong> ${error.message}</div>`;
            } finally {
                isLoading = false;
                updateLoadingState(false);
            }
        }

        // Update loading state
        function updateLoadingState(loading) {
            if (loading) {
                sendBtn.innerHTML = '<span class="loading-spinner"></span> Generating...';
                sendBtn.disabled = true;
                promptInput.disabled = true;
            } else {
                sendBtn.innerHTML = '<span>🚀</span> Generate';
                sendBtn.disabled = false;
                promptInput.disabled = false;
            }
        }

        // Clear input
        function clearInput() {
            promptInput.value = '';
            promptInput.focus();
        }

        // Copy response
        async function copyResponse() {
            try {
                // Extract just the code from the response
                const codeElement = responseArea.querySelector('pre');
                const textToCopy = codeElement ? codeElement.textContent : responseArea.textContent;
                
                await navigator.clipboard.writeText(textToCopy);
                copyBtn.innerHTML = '<span>✅</span> Copied!';
                setTimeout(() => {
                    copyBtn.innerHTML = '<span>📋</span> Copy Response';
                }, 2000);
            } catch (error) {
                console.error('Failed to copy:', error);
            }
        }

        // Update history display
        function updateHistoryDisplay() {
            if (conversationHistory.length === 0) {
                historyContainer.innerHTML = '<p style="color: #8a8886; font-size: 12px; text-align: center;">No conversations yet</p>';
                return;
            }

            historyContainer.innerHTML = conversationHistory.map((item, index) => `
                <div class="history-item" onclick="loadFromHistory(${index})" style="padding: 8px; border: 1px solid #e1e1e1; border-radius: 4px; margin-bottom: 8px; cursor: pointer;">
                    <div style="font-weight: 600; font-size: 12px; color: #0078d4; margin-bottom: 4px;">${item.prompt.substring(0, 40)}...</div>
                    <div style="font-size: 11px; color: #605e5c; margin-bottom: 4px;">${item.response.substring(0, 60)}...</div>
                    <div style="font-size: 10px; color: #8a8886;">${item.timestamp.toLocaleTimeString()} • T=${item.parameters.temperature} • ${item.stats.tokens || 0} tokens</div>
                </div>
            `).join('');
        }

        // Load from history
        function loadFromHistory(index) {
            const item = conversationHistory[index];
            promptInput.value = item.prompt;
            
            // Restore parameters from history
            if (item.parameters) {
                Object.keys(sliders).forEach(sliderId => {
                    const paramName = sliderId.replace('-', '_');
                    if (item.parameters[paramName] !== undefined) {
                        const slider = sliders[sliderId].element;
                        if (slider) {
                            slider.value = item.parameters[paramName];
                            updateSliderValue(sliderId);
                        }
                    }
                });
            }
            
            // Show the response
            responseArea.innerHTML = `<pre style="background: #f8f8f8; padding: 16px; border-radius: 4px; overflow-x: auto; white-space: pre-wrap;">${item.response}</pre>`;
        }

        // Initialize the app
        initialize();
    </script>
</body>
</html>
