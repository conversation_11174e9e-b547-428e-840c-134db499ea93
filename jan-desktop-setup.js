#!/usr/bin/env node
// ─────────────────────────────────────────────────────────────────────────────
// ChatGPT‑Style Desktop App for Jan (Code‑Morningstar) - Setup Script
// Creates a standalone Electron desktop application with ChatGPT-style UI
// - Native desktop app with system tray integration
// - Direct Jan.ai API integration (no proxy needed)
// - Streaming responses with code highlighting
// - Packagable as .exe/.dmg/.AppImage
// ─────────────────────────────────────────────────────────────────────────────

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Electron package.json
const PACKAGE_JSON = `{
  "name": "jan-chatgpt-desktop",
  "version": "1.0.0",
  "description": "ChatGPT-style desktop app for Jan.ai",
  "main": "main.js",
  "scripts": {
    "start": "electron .",
    "dev": "electron . --dev",
    "build": "electron-builder",
    "build-win": "electron-builder --win",
    "build-mac": "electron-builder --mac",
    "build-linux": "electron-builder --linux"
  },
  "dependencies": {
    "electron": "^27.0.0",
    "node-fetch": "^3.3.2"
  },
  "devDependencies": {
    "electron-builder": "^24.6.4"
  },
  "build": {
    "appId": "com.codemorningstar.jan-desktop",
    "productName": "Jan ChatGPT Desktop",
    "directories": {
      "output": "dist"
    },
    "files": [
      "main.js",
      "preload.js",
      "renderer.html",
      "package.json"
    ],
    "win": {
      "target": "nsis",
      "icon": "assets/icon.ico"
    },
    "mac": {
      "target": "dmg",
      "icon": "assets/icon.icns"
    },
    "linux": {
      "target": "AppImage",
      "icon": "assets/icon.png"
    }
  },
  "keywords": ["jan", "chatgpt", "desktop", "ai", "electron"],
  "author": "Code-Morningstar",
  "license": "MIT"
}`;

// Electron main process
const MAIN_JS = `const { app, BrowserWindow, ipcMain, Menu, Tray } = require('electron');
const path = require('path');
const fetch = require('node-fetch');

let mainWindow;
let tray;

// Jan.ai configuration
const JAN_BASE_URL = process.env.JAN_BASE_URL || 'http://127.0.0.1:1337/v1';
const JAN_API_KEY = process.env.JAN_API_KEY || '';

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    titleBarStyle: 'default',
    show: false,
    icon: path.join(__dirname, 'assets', 'icon.png')
  });

  mainWindow.loadFile('renderer.html');
  
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Create system tray
  createTray();
}

function createTray() {
  tray = new Tray(path.join(__dirname, 'assets', 'icon.png'));
  const contextMenu = Menu.buildFromTemplate([
    { label: 'Show', click: () => mainWindow.show() },
    { label: 'Hide', click: () => mainWindow.hide() },
    { type: 'separator' },
    { label: 'Quit', click: () => app.quit() }
  ]);
  tray.setContextMenu(contextMenu);
  tray.setToolTip('Jan ChatGPT Desktop');
}

// IPC handlers for Jan.ai API
ipcMain.handle('get-models', async () => {
  try {
    const response = await fetch(\`\${JAN_BASE_URL}/models\`, {
      headers: {
        'Authorization': JAN_API_KEY ? \`Bearer \${JAN_API_KEY}\` : undefined,
        'Content-Type': 'application/json'
      }
    });
    const data = await response.json();
    return { success: true, data };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('chat-completion', async (event, payload) => {
  try {
    const response = await fetch(\`\${JAN_BASE_URL}/chat/completions\`, {
      method: 'POST',
      headers: {
        'Authorization': JAN_API_KEY ? \`Bearer \${JAN_API_KEY}\` : undefined,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    });

    if (payload.stream) {
      // Handle streaming response
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      
      while (true) {
        const { value, done } = await reader.read();
        if (done) break;
        
        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\\n');
        
        for (const line of lines) {
          if (line.startsWith('data:')) {
            const data = line.replace(/^data:\\s*/, '').trim();
            if (data === '[DONE]') {
              event.sender.send('chat-stream-end');
              return;
            }
            try {
              const json = JSON.parse(data);
              event.sender.send('chat-stream-data', json);
            } catch (e) {
              // Ignore parsing errors
            }
          }
        }
      }
    } else {
      const data = await response.json();
      return { success: true, data };
    }
  } catch (error) {
    return { success: false, error: error.message };
  }
});

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// Preload script for secure IPC
const PRELOAD_JS = \`const { contextBridge, ipcRenderer } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  getModels: () => ipcRenderer.invoke('get-models'),
  chatCompletion: (payload) => ipcRenderer.invoke('chat-completion', payload),
  onStreamData: (callback) => ipcRenderer.on('chat-stream-data', callback),
  onStreamEnd: (callback) => ipcRenderer.on('chat-stream-end', callback),
  removeStreamListeners: () => {
    ipcRenderer.removeAllListeners('chat-stream-data');
    ipcRenderer.removeAllListeners('chat-stream-end');
  }
});
\`;

// Renderer HTML with ChatGPT-style UI
const RENDERER_HTML = \`<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Jan ChatGPT Desktop</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
  <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github-dark.min.css"/>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
  <style>
    :root { color-scheme: dark; }
    body {
      background: #0b0f14;
      margin: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    .msg { white-space: pre-wrap; }
    .bubble { border-radius: 1rem; }
    .scrollbars::-webkit-scrollbar { width: 10px; }
    .scrollbars::-webkit-scrollbar-thumb { background: #1f2937; border-radius: 8px; }
    .prose code { white-space: pre-wrap; }
    .codeblock { background: #0b1220; border: 1px solid #1f2a37; border-radius: 12px; padding: 12px; }
    .title-bar { -webkit-app-region: drag; }
    .title-bar button { -webkit-app-region: no-drag; }
  </style>
</head>
<body class="h-screen text-gray-100">
  <div id="root" class="h-full"></div>

  <script type="module">
    const { useState, useEffect, useRef } = React;

    function TitleBar() {
      return (
        React.createElement('div', {
          className: 'title-bar flex items-center justify-between p-2 bg-[#0a0e13] border-b border-gray-800'
        },
          React.createElement('div', { className: 'flex items-center gap-2' },
            React.createElement('div', { className: 'w-3 h-3 rounded-full bg-red-500' }),
            React.createElement('div', { className: 'w-3 h-3 rounded-full bg-yellow-500' }),
            React.createElement('div', { className: 'w-3 h-3 rounded-full bg-green-500' })
          ),
          React.createElement('div', { className: 'text-sm font-medium' }, 'Jan ChatGPT Desktop'),
          React.createElement('div', { className: 'w-16' }) // spacer
        )
      );
    }

    function Toolbar({ models, currentModel, setModel, temp, setTemp, onClear }) {
      return (
        React.createElement('div', { className: 'flex items-center gap-3 p-3 border-b border-gray-800 bg-[#0f1520]' },
          React.createElement('div', { className: 'text-lg font-semibold' }, '🤖 Jan AI'),
          React.createElement('select', {
            className: 'bg-gray-900 border border-gray-700 rounded-md px-2 py-1 text-sm',
            value: currentModel,
            onChange: e => setModel(e.target.value)
          }, models.map(m => React.createElement('option', { key: m, value: m }, m))),
          React.createElement('div', { className: 'flex items-center gap-2 ml-auto' },
            React.createElement('label', { className: 'text-sm text-gray-400' }, \\\`temp: \\\${temp.toFixed(2)}\\\`),
            React.createElement('input', {
              type: 'range', min: 0, max: 1, step: 0.05, value: temp,
              className: 'w-20',
              onChange: e => setTemp(parseFloat(e.target.value))
            }),
            React.createElement('button', {
              className: 'px-3 py-1 rounded-md bg-gray-800 hover:bg-gray-700 border border-gray-700 text-sm',
              onClick: onClear
            }, 'Clear')
          )
        )
      );
    }\`;
