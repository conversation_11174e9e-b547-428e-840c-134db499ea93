name: Code-Morningstar-Bootstrap-CI

on:
  workflow_dispatch:
  push:
    paths:
      - '.github/workflows/Code-Morningstar-Bootstrap-CI.yml'
      - 'Code-Morningstar.ps1'

jobs:
  bootstrap-ci:
    runs-on: windows-latest

    steps:
    - name: Checkout Repository
      uses: actions/checkout@v4

    - name: List Repository Contents
      shell: pwsh
      run: |
        Write-Host "=== Current Directory ==="
        Write-Host "Working Directory: $(Get-Location)"
        
        Write-Host "`n=== Repository Root Contents ==="
        Get-ChildItem -Force | Format-Table Name, Mode, Length -AutoSize
        
        Write-Host "`n=== PowerShell Scripts ==="
        $psFiles = Get-ChildItem -Name "*.ps1" -ErrorAction SilentlyContinue
        if ($psFiles) {
          $psFiles | ForEach-Object { Write-Host "Found: $_" }
        } else {
          Write-Host "No PowerShell scripts found"
        }

    - name: Execute Bootstrap Script
      shell: pwsh
      run: |
        if (Test-Path "Code-Morningstar.ps1") {
          Write-Host "✅ Script found, setting execution policy and running..."
          Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force
          try {
            & ".\Code-Morningstar.ps1"
            Write-Host "🎉 Script executed successfully!"
          } catch {
            Write-Host "❌ Script execution failed: $_"
            exit 1
          }
        } else {
          Write-Host "❌ Code-Morningstar.ps1 not found in repository root"
          Write-Host "Available files:"
          Get-ChildItem -Name
          exit 1
        }

