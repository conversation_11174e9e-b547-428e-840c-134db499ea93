// ─────────────────────────────────────────────────────────────────────────────
// ChatGPT‑Style UI for Code-Morningstar
// Node proxy + React UI with streaming support for Code-Morningstar's OpenAI API
// - Proxy (server.js) connects to Code-Morningstar's OpenAI-compatible API
// - UI (index.html) provides ChatGPT-like interface optimized for coding
// Ports: UI Proxy 5175, Code-Morningstar OpenAI API 1337, Main API 8000
// ─────────────────────────────────────────────────────────────────────────────

import express from "express";
import cors from "cors";
import fetch from "node-fetch";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Code-Morningstar API endpoints
const CODE_MORNINGSTAR_OPENAI_URL = process.env.CODE_MORNINGSTAR_OPENAI_URL || "http://127.0.0.1:1337/v1";
const CODE_MORNINGSTAR_API_URL = process.env.CODE_MORNINGSTAR_API_URL || "http://127.0.0.1:8000";
const UI_PORT = Number(process.env.UI_PORT || 5175);

const app = express();
app.use(cors());
app.use(express.json({ limit: "2mb" }));

// Serve static UI
app.use(express.static(path.join(__dirname, "public")));

// Health check - check both Code-Morningstar APIs
app.get("/health", async (_, res) => {
  try {
    // Check OpenAI-compatible API
    const openaiCheck = await fetch(`${CODE_MORNINGSTAR_OPENAI_URL.replace('/v1', '')}/health`).catch(() => null);

    // Check main API
    const mainCheck = await fetch(`${CODE_MORNINGSTAR_API_URL}/llm/health`).catch(() => null);

    res.json({
      ok: true,
      code_morningstar_openai: CODE_MORNINGSTAR_OPENAI_URL,
      code_morningstar_main: CODE_MORNINGSTAR_API_URL,
      openai_api_status: openaiCheck ? await openaiCheck.text() : "unavailable",
      main_api_status: mainCheck ? await mainCheck.text() : "unavailable"
    });
  } catch (error) {
    res.status(500).json({ ok: false, error: error.message });
  }
});

// List models (proxy to Code-Morningstar OpenAI API)
app.get("/v1/models", async (req, res) => {
  try {
    const response = await fetch(`${CODE_MORNINGSTAR_OPENAI_URL}/models`, {
      headers: {
        "Content-Type": "application/json"
      }
    });

    if (!response.ok) {
      throw new Error(`Code-Morningstar API returned ${response.status}`);
    }

    res.status(response.status);
    response.body.pipe(res);
  } catch (error) {
    console.error("Models API error:", error);
    res.status(500).json({ error: error.message });
  }
});

// Chat completions (proxy to Code-Morningstar OpenAI API with streaming support)
app.post("/v1/chat/completions", async (req, res) => {
  try {
    const body = req.body || {};
    const stream = Boolean(body.stream);

    const response = await fetch(`${CODE_MORNINGSTAR_OPENAI_URL}/chat/completions`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(body)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Code-Morningstar API error: ${response.status} - ${errorText}`);
    }

    // Pass through status and headers
    res.status(response.status);
    if (stream) {
      res.setHeader("Content-Type", "text/event-stream");
      res.setHeader("Cache-Control", "no-cache");
      res.setHeader("Connection", "keep-alive");
    } else {
      res.setHeader("Content-Type", "application/json");
    }

    response.body.pipe(res);
  } catch (error) {
    console.error("Chat completions error:", error);
    res.status(500).json({ error: error.message });
  }
});

// Additional endpoint to check Code-Morningstar status
app.get("/code-morningstar/status", async (req, res) => {
  try {
    const healthResponse = await fetch(`${CODE_MORNINGSTAR_API_URL}/llm/health`);
    const healthData = await healthResponse.json();

    res.json({
      status: "connected",
      health: healthData,
      openai_api: CODE_MORNINGSTAR_OPENAI_URL,
      main_api: CODE_MORNINGSTAR_API_URL
    });
  } catch (error) {
    res.status(500).json({
      status: "disconnected",
      error: error.message,
      openai_api: CODE_MORNINGSTAR_OPENAI_URL,
      main_api: CODE_MORNINGSTAR_API_URL
    });
  }
});

app.listen(UI_PORT, () => {
  console.log(`🌟 Code-Morningstar ChatGPT-Style UI`);
  console.log(`📡 UI + Proxy: http://127.0.0.1:${UI_PORT}`);
  console.log(`🔗 Code-Morningstar OpenAI API: ${CODE_MORNINGSTAR_OPENAI_URL}`);
  console.log(`🔗 Code-Morningstar Main API: ${CODE_MORNINGSTAR_API_URL}`);
  console.log(`📋 Health Check: http://127.0.0.1:${UI_PORT}/health`);
});
