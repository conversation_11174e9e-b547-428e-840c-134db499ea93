#!/usr/bin/env python3
"""
Code Morningstar Complete Desktop App
Starts the server automatically and provides AI interface
No terminal needed!
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import subprocess
import json
import threading
import time
import os
import sys
from pathlib import Path
from datetime import datetime
import signal
import atexit
import urllib.request
import urllib.parse
import urllib.error

class CodeMorningstarDesktop:
    def __init__(self, root):
        self.root = root
        self.server_process = None
        self.api_base = "http://localhost:8002"  # Updated to avoid conflict with your API tower
        self.backend_path = Path("backend")
        
        self.setup_window()
        self.setup_styles()
        self.create_widgets()
        
        # Register cleanup on exit
        atexit.register(self.cleanup)
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # Start server automatically
        self.start_server()
        
    def setup_window(self):
        """Setup main window"""
        self.root.title("🌟 Code Morningstar v2.0 - Complete AI Desktop")
        self.root.geometry("1400x900")
        self.root.minsize(1000, 700)
        
        # Modern colors
        self.colors = {
            'bg': '#0d1117',
            'fg': '#f0f6fc',
            'accent': '#58a6ff',
            'success': '#3fb950',
            'warning': '#d29922',
            'error': '#f85149',
            'card': '#161b22',
            'border': '#30363d'
        }
        
        self.root.configure(bg=self.colors['bg'])
        
    def setup_styles(self):
        """Setup modern styles"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure styles
        style.configure('Title.TLabel', 
                       background=self.colors['bg'], 
                       foreground=self.colors['accent'],
                       font=('Segoe UI', 18, 'bold'))
        
        style.configure('Status.TLabel',
                       background=self.colors['bg'],
                       foreground=self.colors['success'],
                       font=('Segoe UI', 11))
        
        style.configure('Modern.TButton',
                       background=self.colors['accent'],
                       foreground='white',
                       font=('Segoe UI', 11, 'bold'),
                       padding=(15, 8))
        
        style.configure('Modern.TFrame',
                       background=self.colors['bg'])
        
    def create_widgets(self):
        """Create all widgets"""
        # Main container
        main_frame = ttk.Frame(self.root, style='Modern.TFrame')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=25, pady=25)
        
        # Header
        self.create_header(main_frame)
        
        # Server status
        self.create_server_status(main_frame)
        
        # Main content
        content_frame = ttk.Frame(main_frame, style='Modern.TFrame')
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(15, 0))
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(content_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # AI Chat tab
        self.create_chat_tab()
        
        # Server Control tab
        self.create_server_tab()
        
        # Settings tab
        self.create_settings_tab()
        
    def create_header(self, parent):
        """Create header"""
        header_frame = ttk.Frame(parent, style='Modern.TFrame')
        header_frame.pack(fill=tk.X, pady=(0, 15))
        
        title_label = ttk.Label(header_frame, 
                               text="🌟 Code Morningstar v2.0", 
                               style='Title.TLabel')
        title_label.pack(side=tk.LEFT)
        
        subtitle_label = ttk.Label(header_frame,
                                  text="Complete AI Desktop - No Terminal Required!",
                                  background=self.colors['bg'],
                                  foreground=self.colors['fg'],
                                  font=('Segoe UI', 12))
        subtitle_label.pack(side=tk.LEFT, padx=(15, 0))
        
    def create_server_status(self, parent):
        """Create server status bar"""
        status_frame = ttk.Frame(parent, style='Modern.TFrame')
        status_frame.pack(fill=tk.X, pady=(0, 15))
        
        # Server status
        self.server_status_label = ttk.Label(status_frame,
                                           text="🔄 Starting server...",
                                           style='Status.TLabel')
        self.server_status_label.pack(side=tk.LEFT)
        
        # Model info
        self.model_info_label = ttk.Label(status_frame,
                                         text="Model: Loading...",
                                         background=self.colors['bg'],
                                         foreground=self.colors['fg'],
                                         font=('Segoe UI', 10))
        self.model_info_label.pack(side=tk.RIGHT)
        
    def create_chat_tab(self):
        """Create AI chat interface tab"""
        chat_frame = ttk.Frame(self.notebook, style='Modern.TFrame')
        self.notebook.add(chat_frame, text="🤖 AI Assistant")
        
        # Chat container
        chat_container = ttk.Frame(chat_frame, style='Modern.TFrame')
        chat_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Input section
        input_frame = ttk.Frame(chat_container, style='Modern.TFrame')
        input_frame.pack(fill=tk.BOTH, expand=True)
        
        # Left side - Input
        left_frame = ttk.Frame(input_frame, style='Modern.TFrame')
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        ttk.Label(left_frame, text="💬 Your Coding Prompt:",
                 background=self.colors['bg'], foreground=self.colors['fg'],
                 font=('Segoe UI', 14, 'bold')).pack(anchor=tk.W, pady=(0, 10))
        
        self.input_text = scrolledtext.ScrolledText(
            left_frame, height=12, font=('Consolas', 12),
            bg=self.colors['card'], fg=self.colors['fg'],
            insertbackground=self.colors['fg'],
            selectbackground=self.colors['accent'],
            relief='solid', borderwidth=1
        )
        self.input_text.pack(fill=tk.BOTH, expand=True)
        
        # Right side - Output
        right_frame = ttk.Frame(input_frame, style='Modern.TFrame')
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        ttk.Label(right_frame, text="🤖 AI Response:",
                 background=self.colors['bg'], foreground=self.colors['fg'],
                 font=('Segoe UI', 14, 'bold')).pack(anchor=tk.W, pady=(0, 10))
        
        self.output_text = scrolledtext.ScrolledText(
            right_frame, height=12, font=('Consolas', 12),
            bg=self.colors['card'], fg=self.colors['fg'],
            insertbackground=self.colors['fg'],
            selectbackground=self.colors['accent'],
            relief='solid', borderwidth=1, state=tk.DISABLED
        )
        self.output_text.pack(fill=tk.BOTH, expand=True)
        
        # Controls
        controls_frame = ttk.Frame(chat_container, style='Modern.TFrame')
        controls_frame.pack(fill=tk.X, pady=(15, 0))
        
        # Parameters
        params_frame = ttk.Frame(controls_frame, style='Modern.TFrame')
        params_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # Temperature
        temp_frame = ttk.Frame(params_frame, style='Modern.TFrame')
        temp_frame.pack(side=tk.LEFT, padx=(0, 30))
        ttk.Label(temp_frame, text="Temperature (Precision):",
                 background=self.colors['bg'], foreground=self.colors['fg']).pack()
        self.temp_var = tk.DoubleVar(value=0.1)
        temp_scale = ttk.Scale(temp_frame, from_=0.0, to=1.0, 
                              variable=self.temp_var, length=120)
        temp_scale.pack()
        self.temp_label = ttk.Label(temp_frame, text="0.1 (Max Precision)",
                                   background=self.colors['bg'], foreground=self.colors['fg'])
        self.temp_label.pack()
        temp_scale.configure(command=self.update_temp_label)
        
        # Max tokens
        tokens_frame = ttk.Frame(params_frame, style='Modern.TFrame')
        tokens_frame.pack(side=tk.LEFT)
        ttk.Label(tokens_frame, text="Response Length:",
                 background=self.colors['bg'], foreground=self.colors['fg']).pack()
        self.tokens_var = tk.IntVar(value=4096)
        tokens_scale = ttk.Scale(tokens_frame, from_=512, to=8192,
                                variable=self.tokens_var, length=120)
        tokens_scale.pack()
        self.tokens_label = ttk.Label(tokens_frame, text="4096 tokens",
                                     background=self.colors['bg'], foreground=self.colors['fg'])
        self.tokens_label.pack()
        tokens_scale.configure(command=self.update_tokens_label)
        
        # Buttons
        buttons_frame = ttk.Frame(controls_frame, style='Modern.TFrame')
        buttons_frame.pack(side=tk.RIGHT)
        
        self.generate_btn = ttk.Button(buttons_frame, text="🚀 Generate AI Response",
                                      style='Modern.TButton', command=self.generate_response)
        self.generate_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(buttons_frame, text="🗑️ Clear All", 
                  command=self.clear_all).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(buttons_frame, text="📋 Copy Response",
                  command=self.copy_response).pack(side=tk.LEFT)
        
        # Add example prompts
        self.add_example_prompts(left_frame)

    def add_example_prompts(self, parent):
        """Add example prompts"""
        examples_frame = ttk.Frame(parent, style='Modern.TFrame')
        examples_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Label(examples_frame, text="💡 Quick Examples:",
                 background=self.colors['bg'], foreground=self.colors['fg'],
                 font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W)

        examples = [
            "Create a Python web scraper with async/await",
            "Design a REST API for user authentication",
            "Optimize this algorithm for better performance",
            "Analyze code for security vulnerabilities"
        ]

        for i, example in enumerate(examples):
            btn = ttk.Button(examples_frame, text=f"{i+1}. {example}",
                           command=lambda e=example: self.set_example_prompt(e))
            btn.pack(fill=tk.X, pady=1)

    def set_example_prompt(self, prompt):
        """Set example prompt in input"""
        self.input_text.delete('1.0', tk.END)
        self.input_text.insert('1.0', prompt)

    def create_server_tab(self):
        """Create server control tab"""
        server_frame = ttk.Frame(self.notebook, style='Modern.TFrame')
        self.notebook.add(server_frame, text="🖥️ Server Control")

        container = ttk.Frame(server_frame, style='Modern.TFrame')
        container.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)

        # Server status
        status_frame = ttk.Frame(container, style='Modern.TFrame')
        status_frame.pack(fill=tk.X, pady=(0, 20))

        ttk.Label(status_frame, text="🖥️ Server Status",
                 background=self.colors['bg'], foreground=self.colors['fg'],
                 font=('Segoe UI', 16, 'bold')).pack(anchor=tk.W)

        self.detailed_status = ttk.Label(status_frame, text="Starting...",
                                        background=self.colors['bg'], foreground=self.colors['fg'],
                                        font=('Segoe UI', 12))
        self.detailed_status.pack(anchor=tk.W, pady=(5, 0))

        # Server controls
        controls_frame = ttk.Frame(container, style='Modern.TFrame')
        controls_frame.pack(fill=tk.X, pady=(0, 20))

        ttk.Button(controls_frame, text="🔄 Restart Server",
                  style='Modern.TButton', command=self.restart_server).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(controls_frame, text="⏹️ Stop Server",
                  command=self.stop_server).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(controls_frame, text="🌐 Open API Docs",
                  command=self.open_api_docs).pack(side=tk.LEFT)

        # Server logs
        ttk.Label(container, text="📋 Server Logs",
                 background=self.colors['bg'], foreground=self.colors['fg'],
                 font=('Segoe UI', 14, 'bold')).pack(anchor=tk.W, pady=(20, 5))

        self.log_text = scrolledtext.ScrolledText(
            container, height=15, font=('Consolas', 10),
            bg=self.colors['card'], fg=self.colors['fg'],
            insertbackground=self.colors['fg'],
            relief='solid', borderwidth=1, state=tk.DISABLED
        )
        self.log_text.pack(fill=tk.BOTH, expand=True)

    def create_settings_tab(self):
        """Create settings tab"""
        settings_frame = ttk.Frame(self.notebook, style='Modern.TFrame')
        self.notebook.add(settings_frame, text="⚙️ Settings")

        container = ttk.Frame(settings_frame, style='Modern.TFrame')
        container.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)

        ttk.Label(container, text="⚙️ Code Morningstar Settings",
                 background=self.colors['bg'], foreground=self.colors['fg'],
                 font=('Segoe UI', 16, 'bold')).pack(anchor=tk.W, pady=(0, 20))

        # Model info
        model_frame = ttk.Frame(container, style='Modern.TFrame')
        model_frame.pack(fill=tk.X, pady=(0, 20))

        ttk.Label(model_frame, text="🤖 AI Model Information",
                 background=self.colors['bg'], foreground=self.colors['fg'],
                 font=('Segoe UI', 14, 'bold')).pack(anchor=tk.W)

        self.model_details = ttk.Label(model_frame,
                                      text="Model: CodeLlama 34B Q4_K_M\nPrecision: Maximum (Q4_K_M quantization)\nContext: 8192 tokens\nOptimized for: RTX 4070",
                                      background=self.colors['bg'], foreground=self.colors['fg'],
                                      font=('Segoe UI', 11))
        self.model_details.pack(anchor=tk.W, pady=(5, 0))

        # Performance info
        perf_frame = ttk.Frame(container, style='Modern.TFrame')
        perf_frame.pack(fill=tk.X, pady=(0, 20))

        ttk.Label(perf_frame, text="⚡ Performance Information",
                 background=self.colors['bg'], foreground=self.colors['fg'],
                 font=('Segoe UI', 14, 'bold')).pack(anchor=tk.W)

        perf_text = """GPU: RTX 4070 (12GB VRAM)
GPU Layers: 50 (Maximum utilization)
Expected Speed: 1-3 tokens/second
Quality Level: Professional-grade
Intelligence: Maximum precision mode"""

        ttk.Label(perf_frame, text=perf_text,
                 background=self.colors['bg'], foreground=self.colors['fg'],
                 font=('Segoe UI', 11)).pack(anchor=tk.W, pady=(5, 0))

    def start_server(self):
        """Start the backend server"""
        def start():
            try:
                self.log_message("🚀 Starting Code Morningstar backend server...")

                # Change to backend directory and start server
                cmd = [sys.executable, "main.py"]

                self.server_process = subprocess.Popen(
                    cmd,
                    cwd=self.backend_path,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    universal_newlines=True,
                    bufsize=1
                )

                # Monitor server output
                self.monitor_server_output()

                # Wait a bit then check if server is running
                time.sleep(3)
                self.check_server_health()

            except Exception as e:
                self.root.after(0, self.log_message, f"❌ Failed to start server: {e}")

        threading.Thread(target=start, daemon=True).start()

    def monitor_server_output(self):
        """Monitor server output in background"""
        def monitor():
            if self.server_process:
                for line in iter(self.server_process.stdout.readline, ''):
                    if line:
                        self.root.after(0, self.log_message, line.strip())

        threading.Thread(target=monitor, daemon=True).start()

    def check_server_health(self):
        """Check server health"""
        def check():
            try:
                req = urllib.request.Request(f"{self.api_base}/llm/health")
                with urllib.request.urlopen(req, timeout=5) as response:
                    if response.status == 200:
                        data = json.loads(response.read().decode())
                        self.root.after(0, self.update_server_status, True, data)
                    else:
                        self.root.after(0, self.update_server_status, False, None)
            except:
                # Server might still be starting, try again
                time.sleep(2)
                try:
                    req = urllib.request.Request(f"{self.api_base}/llm/health")
                    with urllib.request.urlopen(req, timeout=5) as response:
                        if response.status == 200:
                            data = json.loads(response.read().decode())
                            self.root.after(0, self.update_server_status, True, data)
                        else:
                            self.root.after(0, self.update_server_status, False, None)
                except:
                    self.root.after(0, self.update_server_status, False, None)

        threading.Thread(target=check, daemon=True).start()

    def update_server_status(self, is_healthy, data):
        """Update server status display"""
        if is_healthy and data:
            self.server_status_label.config(text="✅ Server Online - AI Ready!")
            model_name = data.get('model_path', '').split('/')[-1]
            self.model_info_label.config(text=f"Model: {model_name}")
            self.detailed_status.config(text="✅ Server running successfully\n🤖 AI model loaded and ready\n🚀 Ready for code generation")
            self.generate_btn.config(state='normal')
        else:
            self.server_status_label.config(text="❌ Server Starting...")
            self.model_info_label.config(text="Model: Loading...")
            self.detailed_status.config(text="🔄 Server is starting up...\n⏳ Please wait for AI model to load")
            self.generate_btn.config(state='disabled')

    def log_message(self, message):
        """Add message to server logs"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        formatted_message = f"[{timestamp}] {message}\n"

        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, formatted_message)
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)

    def restart_server(self):
        """Restart the server"""
        self.log_message("🔄 Restarting server...")
        self.stop_server()
        time.sleep(2)
        self.start_server()

    def stop_server(self):
        """Stop the server"""
        if self.server_process:
            self.log_message("⏹️ Stopping server...")
            self.server_process.terminate()
            self.server_process = None
            self.server_status_label.config(text="⏹️ Server Stopped")
            self.generate_btn.config(state='disabled')

    def open_api_docs(self):
        """Open API documentation in browser"""
        import webbrowser
        webbrowser.open(f"{self.api_base}/docs")

    def update_temp_label(self, value):
        """Update temperature label"""
        temp = float(value)
        if temp <= 0.1:
            desc = "Max Precision"
        elif temp <= 0.3:
            desc = "High Precision"
        elif temp <= 0.7:
            desc = "Balanced"
        else:
            desc = "Creative"
        self.temp_label.config(text=f"{temp:.2f} ({desc})")

    def update_tokens_label(self, value):
        """Update tokens label"""
        tokens = int(float(value))
        self.tokens_label.config(text=f"{tokens} tokens")

    def generate_response(self):
        """Generate AI response"""
        prompt = self.input_text.get('1.0', tk.END).strip()

        if not prompt:
            messagebox.showwarning("Warning", "Please enter a prompt first!")
            return

        # Disable button during generation
        self.generate_btn.config(state='disabled', text='🔄 AI Thinking...')

        # Clear previous output
        self.output_text.config(state=tk.NORMAL)
        self.output_text.delete('1.0', tk.END)
        self.output_text.insert('1.0', "🤖 Code Morningstar AI is analyzing your request...\n\n⚡ Using maximum intelligence mode\n🧠 Processing with 34B parameters\n🎯 Q4_K_M precision active\n\nThis may take 30-90 seconds for complex requests.\nPlease wait...\n\n")
        self.output_text.config(state=tk.DISABLED)

        # Generate in background
        def generate():
            try:
                payload = {
                    "prompt": prompt,
                    "temperature": self.temp_var.get(),
                    "max_tokens": self.tokens_var.get(),
                    "top_p": 0.95,
                    "top_k": 50,
                    "repeat_penalty": 1.05
                }

                start_time = time.time()

                # Prepare POST request
                data = json.dumps(payload).encode('utf-8')
                req = urllib.request.Request(f"{self.api_base}/llm/generate",
                                           data=data,
                                           headers={'Content-Type': 'application/json'})

                with urllib.request.urlopen(req, timeout=600) as response:  # 10 minute timeout
                    if response.status == 200:
                        response_data = json.loads(response.read().decode())
                        result = response_data.get('result', 'No response generated')
                        processing_time = response_data.get('processing_time', time.time() - start_time)
                        tokens_used = response_data.get('tokens_used', 0)

                        # Format response
                        formatted_response = f"""🌟 CODE MORNINGSTAR AI RESPONSE
{'='*60}
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Processing Time: {processing_time:.2f} seconds
Tokens Generated: {tokens_used}
Model: CodeLlama 34B Q4_K_M (Maximum Intelligence)
Temperature: {self.temp_var.get():.2f}
Max Tokens: {self.tokens_var.get()}
{'='*60}

{result}

{'='*60}
✨ Response generated with professional-grade AI
🚀 Powered by Code Morningstar v2.0"""

                        self.root.after(0, self.display_response, formatted_response, True)
                    else:
                        error_msg = f"❌ Error {response.status}: Server error"
                        self.root.after(0, self.display_response, error_msg, False)

            except urllib.error.URLError as e:
                if "timeout" in str(e).lower():
                    error_msg = "⏰ Request timed out. The AI might be processing a very complex request.\nTry a simpler prompt or restart the server."
                else:
                    error_msg = f"❌ Network Error: {str(e)}\n\nMake sure the server is running properly!"
                self.root.after(0, self.display_response, error_msg, False)
            except Exception as e:
                error_msg = f"❌ Error: {str(e)}\n\nMake sure the server is running properly!"
                self.root.after(0, self.display_response, error_msg, False)

        threading.Thread(target=generate, daemon=True).start()

    def display_response(self, response, success):
        """Display the AI response"""
        self.output_text.config(state=tk.NORMAL)
        self.output_text.delete('1.0', tk.END)
        self.output_text.insert('1.0', response)
        self.output_text.config(state=tk.DISABLED)

        # Re-enable button
        self.generate_btn.config(state='normal', text='🚀 Generate AI Response')

        if success:
            self.log_message("✅ AI response generated successfully")
        else:
            self.log_message("❌ AI generation failed")

    def clear_all(self):
        """Clear all text areas"""
        self.input_text.delete('1.0', tk.END)
        self.output_text.config(state=tk.NORMAL)
        self.output_text.delete('1.0', tk.END)
        self.output_text.config(state=tk.DISABLED)

    def copy_response(self):
        """Copy response to clipboard"""
        response = self.output_text.get('1.0', tk.END).strip()
        if response:
            self.root.clipboard_clear()
            self.root.clipboard_append(response)
            messagebox.showinfo("Success", "Response copied to clipboard!")
        else:
            messagebox.showwarning("Warning", "No response to copy!")

    def cleanup(self):
        """Cleanup when closing"""
        if self.server_process:
            self.server_process.terminate()

    def on_closing(self):
        """Handle window closing"""
        if messagebox.askokcancel("Quit", "Do you want to quit Code Morningstar?\n\nThis will stop the AI server."):
            self.cleanup()
            self.root.destroy()

def main():
    """Main function"""
    # Check if backend directory exists
    if not Path("backend").exists():
        messagebox.showerror("Error", "Backend directory not found!\n\nPlease run this from the Code-Morningstar root directory.")
        return

    root = tk.Tk()
    app = CodeMorningstarDesktop(root)

    # Center window
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")

    try:
        root.mainloop()
    except KeyboardInterrupt:
        app.cleanup()

if __name__ == "__main__":
    main()
