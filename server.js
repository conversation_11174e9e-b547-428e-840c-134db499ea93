import express from "express";
import cors from "cors";
import fetch from "node-fetch";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const JAN_BASE_URL = process.env.JAN_BASE_URL || "http://127.0.0.1:1337/v1";
const JAN_API_KEY  = process.env.JAN_API_KEY  || ""; // if Jan is open, can be blank
const UI_PORT      = Number(process.env.UI_PORT || 5175);

const app = express();
app.use(cors());
app.use(express.json({ limit: "2mb" }));

// Serve static UI
app.use(express.static(path.join(__dirname, "public")));

// Health
app.get("/health", (_, res) => res.json({ ok: true, jan: <PERSON><PERSON>_BASE_URL }));

// List models (pass through to Jan)
app.get("/v1/models", async (req, res) => {
  const r = await fetch(`${JAN_BASE_URL}/models`, {
    headers: {
      "Authorization": JAN_API_KEY ? `Bearer ${JAN_API_KEY}` : undefined,
      "Content-Type": "application/json"
    }
  });
  res.status(r.status);
  r.body.pipe(res);
});

// Chat completions (streaming or non‑streaming). We stream if client sets stream=true
app.post("/v1/chat/completions", async (req, res) => {
  const body = req.body || {};
  const stream = Boolean(body.stream);
  const upstream = await fetch(`${JAN_BASE_URL}/chat/completions`, {
    method: "POST",
    headers: {
      "Authorization": JAN_API_KEY ? `Bearer ${JAN_API_KEY}` : undefined,
      "Content-Type": "application/json"
    },
    body: JSON.stringify(body)
  });

  // Pass status and headers
  res.status(upstream.status);
  if (stream) {
    res.setHeader("Content-Type", "text/event-stream");
    res.setHeader("Cache-Control", "no-cache");
    res.setHeader("Connection", "keep-alive");
  } else {
    res.setHeader("Content-Type", "application/json");
  }

  upstream.body.pipe(res);
});

app.listen(UI_PORT, () => {
  console.log(`UI + Proxy on http://127.0.0.1:${UI_PORT}`);
  console.log(`Proxying Jan at ${JAN_BASE_URL}`);
});