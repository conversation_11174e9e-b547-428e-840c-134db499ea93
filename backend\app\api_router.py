from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Optional
import logging
from backend.services.llm_service import LLMService
from backend.settings import get_settings

logger = logging.getLogger(__name__)

class GenerateRequest(BaseModel):
    prompt: str
    max_tokens: Optional[int] = 512
    temperature: Optional[float] = 0.7

class GenerateResponse(BaseModel):
    response: str
    model_loaded: bool
    model_info: dict

class HealthResponse(BaseModel):
    status: str
    model_loaded: bool
    model_info: dict

api_router = APIRouter()
llm_service: Optional[LLMService] = None

def get_llm_service():
    global llm_service
    if llm_service is None:
        settings = get_settings()
        llm_service = LLMService(settings.llm_model_path)
    return llm_service

@api_router.get("/llm/health", response_model=HealthResponse)
async def health_check(service: LLMService = Depends(get_llm_service)):
    try:
        model_info = service.get_model_info()
        return HealthResponse(
            status="healthy",
            model_loaded=service.is_model_loaded(),
            model_info=model_info
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@api_router.post("/llm/generate", response_model=GenerateResponse)
async def generate_text(
    request: GenerateRequest,
    service: LLMService = Depends(get_llm_service)
):
    try:
        if not request.prompt.strip():
            raise HTTPException(status_code=400, detail="Prompt cannot be empty")
        
        response = await service.generate_async(
            prompt=request.prompt,
            max_tokens=request.max_tokens or 512,
            temperature=request.temperature or 0.7
        )
        
        return GenerateResponse(
            response=response,
            model_loaded=service.is_model_loaded(),
            model_info=service.get_model_info()
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Generation failed: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@api_router.get("/llm/model-info")
async def get_model_info(service: LLMService = Depends(get_llm_service)):
    return service.get_model_info()
