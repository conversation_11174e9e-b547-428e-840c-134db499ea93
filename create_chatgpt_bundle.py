#!/usr/bin/env python3
"""
Script to generate a comprehensive Code-Morningstar project bundle for ChatGPT
This creates a clean, organized summary of the entire project.
"""

import os
import json
from pathlib import Path

def get_project_files():
    """Get all relevant project files excluding generated/cache directories"""
    exclude_patterns = {
        '.venv', 'node_modules', '__pycache__', '.pytest_cache', 
        '.vs', 'CopilotSnapshots', '.git', 'dist', 'build'
    }
    
    project_root = Path.cwd()
    files = []
    
    for root, dirs, filenames in os.walk(project_root):
        # Remove excluded directories from dirs list to prevent walking into them
        dirs[:] = [d for d in dirs if not any(pattern in d for pattern in exclude_patterns)]
        
        for filename in filenames:
            file_path = Path(root) / filename
            rel_path = file_path.relative_to(project_root)
            
            # Skip binary files and large files
            if (file_path.suffix in {'.pyc', '.zip', '.gguf', '.db', '.exe', '.dll', '.pyd'} or 
                file_path.stat().st_size > 1024 * 1024):  # Skip files > 1MB
                continue
                
            files.append(str(rel_path))
    
    return sorted(files)

def create_chatgpt_bundle():
    """Create a comprehensive bundle for ChatGPT"""
    
    print("🚀 Creating Code-Morningstar ChatGPT Bundle...")
    
    # Get project structure
    files = get_project_files()
    
    bundle_content = f"""# Code-Morningstar: Complete Project for ChatGPT Analysis

## 📋 Project Overview
Enterprise-grade Python/FastAPI + Svelte application with local LLM integration and multi-database support.

## 📁 Complete File Structure ({len(files)} files)
```
{chr(10).join(files)}
```

## 🔧 Key Information
- **Backend**: FastAPI with 8 database integrations
- **Frontend**: Svelte/TypeScript with Vite
- **LLM**: Local GGUF model inference
- **Infrastructure**: Kubernetes + Terraform
- **CI/CD**: GitHub Actions multi-OS testing

## 📋 What to share with ChatGPT:

### Option 1: Share Repository URL
If your repo is public: https://github.com/yourusername/Code-Morningstar

### Option 2: Key Files Summary
Share these critical files with ChatGPT:

**Core Configuration:**
- README.md
- package.json (root)
- backend/requirements.txt
- backend/settings.py

**Backend Application:**
- backend/app/main.py
- backend/app/api_router.py
- backend/app/llm_api.py
- backend/services/db_router.py
- backend/services/llm_service.py

**Frontend Application:**
- frontend/package.json
- frontend/src/App.svelte
- frontend/src/main.ts

**Infrastructure:**
- .github/workflows/ci.yml
- infra/k8s/backend-deployment.yaml
- infra/terraform/main.tf

**Documentation:**
- docs/API.md
- docs/DEVELOPMENT.md
- ADRs/0001-architecture.md

### Option 3: Complete Project Archive
Use the generated Code-Morningstar-clean.zip file

## 💡 ChatGPT Prompt Template:
"I need help with my Code-Morningstar project. It's an enterprise-grade Python/FastAPI + Svelte application with:
- Multi-database support (PostgreSQL, MySQL, MongoDB, Redis, SQLite, Cassandra, Neo4j, Elasticsearch)
- Local LLM integration via llama-cpp-python
- Kubernetes deployment
- GitHub Actions CI/CD

[Include specific files or ask specific questions about the architecture]"

## 🎯 Current Status:
- Repository exists but some files were accidentally deleted
- Working on 'safe-work' branch  
- Most scaffold files have been restored
- Ready for ChatGPT analysis and assistance
"""

    # Write the bundle
    with open("CHATGPT_BUNDLE.md", "w", encoding="utf-8") as f:
        f.write(bundle_content)
    
    print("✅ Created CHATGPT_BUNDLE.md")
    print("📋 File contains complete project overview for ChatGPT")
    print("\n🎯 Next steps:")
    print("1. Review CHATGPT_BUNDLE.md")
    print("2. Share with ChatGPT using one of the suggested methods")
    print("3. Include specific questions about what you need help with")

if __name__ == "__main__":
    create_chatgpt_bundle()
