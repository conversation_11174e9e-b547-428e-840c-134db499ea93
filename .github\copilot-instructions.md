# Code Morningstar - AI Coding Agent Instructions

## Architecture Overview

Code Morningstar is an enterprise-grade LLM application with a clean FastAPI backend and standalone HTML frontend, designed for local CodeLlama model inference with multi-database support.

### Key Components

- **Backend**: FastAPI with async support at `backend/main.py` and `backend/app/main.py` (dual entry points)
- **LLM Service**: Singleton service at `backend/services/llm_service.py` handling GGUF model loading via llama-cpp-python
- **Frontend**: Standalone HTML files in `frontend/` (no build process required)
- **Configuration**: Pydantic settings with environment-first approach

## Critical Patterns

### 1. Model Path Configuration
Always use the exact model path pattern from `.env`:
```env
LLM_MODEL_PATH=C:\Users\<USER>\Code-Morningstar\models\codellama-7b-instruct.Q8_0.gguf
```
The LLMService requires a valid model file and will raise an error if not found.

### 2. Dependency Injection Pattern
Use FastAPI's dependency injection for LLMService - it's a singleton:
```python
def get_llm_service():
    global llm_service
    if llm_service is None:
        settings = get_settings()
        llm_service = LLMService(settings.llm_model_path)
    return llm_service
```

### 3. Async/Sync Bridge
LLMService uses ThreadPoolExecutor for sync model calls in async context:
```python
async def generate_async(self, prompt: str, max_tokens: int = 512, temperature: float = 0.7) -> str:
    loop = asyncio.get_event_loop()
    return await loop.run_in_executor(self.executor, self._generate_sync, prompt, max_tokens, temperature)
```

### 4. Feature Flags via YAML
Feature flags live in `backend/feature_flags/flags.yaml` - simple key-value pairs loaded by FeatureFlagManager.

## Development Workflows

### Starting the Application
Use the startup scripts that handle both entry points:
- `start_backend.ps1` or `start_backend.bat` for backend only
- `start_app.bat` for full stack (backend + frontend)
- Both point to different main.py files but same FastAPI app

### Testing
Run from project root: `python -m pytest backend/tests/ -v`
Tests use pytest fixtures for mock model files and dependency injection.

### Model Integration
- Models go in `models/` directory at project root
- LLMService handles both file existence checking and graceful mock fallback
- Use `n_gpu_layers=35` for HP Omen 16 hardware optimization (see `.env`)

## Database Architecture

Multi-database support is configured but disabled by default (`ENABLE_DATABASE_SERVICES=false`).
Services in `backend/services/` follow consistent patterns for MongoDB, PostgreSQL, Redis, etc.

## API Design

All endpoints follow this pattern:
- Pydantic models for request/response with validation
- Dependency injection for services
- Structured error handling with HTTPException
- Health checks return model status and file info

Key endpoints:
- `POST /llm/generate` - Text generation with prompt validation
- `GET /llm/health` - Model status and file existence
- `GET /llm/model-info` - Detailed model metadata

## Frontend Integration

Frontend uses direct HTTP calls to backend - no complex build process.
Files: `windows-ui.html`, `standalone.html`, `chat-ui.html` for different UIs.

## Environment Variables

Settings use Pydantic with `.env` file loading. Critical settings:
- `LLM_MODEL_PATH` - Absolute path to GGUF model

- GPU settings for local hardware optimization

## Code Conventions

- Use `backend.` prefix for absolute imports from project root
- Structured logging with logger instances per module
- Pydantic models with Field validation for all API boundaries
- Type hints required for all function signatures
