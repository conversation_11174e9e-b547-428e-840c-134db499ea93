start "" "http://localhost:8000"powershell -Command "Start-Process 'http://localhost:8000'"<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Code Morningstar - Chat Interface</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f0f2f5;
            height: 100vh;
            overflow: hidden;
        }

        .app-container {
            display: flex;
            height: 100vh;
        }

        /* Sidebar with controls */
        .sidebar {
            width: 350px;
            background: white;
            border-right: 1px solid #e0e0e0;
            display: flex;
            flex-direction: column;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
            background: #0078d4;
            color: white;
        }

        .sidebar-header h1 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #00ff00;
        }

        .settings-panel {
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
        }

        .settings-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #323130;
        }

        .setting-item {
            margin-bottom: 16px;
        }

        .setting-label {
            display: block;
            font-size: 13px;
            font-weight: 600;
            color: #605e5c;
            margin-bottom: 8px;
        }

        .slider {
            width: 100%;
            height: 4px;
            background: #e1e1e1;
            border-radius: 2px;
            outline: none;
            -webkit-appearance: none;
            appearance: none;
        }

        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            background: #0078d4;
            border-radius: 50%;
            cursor: pointer;
        }

        .reset-button {
            width: 100%;
            padding: 8px 16px;
            background: #f3f2f1;
            border: 1px solid #e1e1e1;
            border-radius: 4px;
            color: #323130;
            font-size: 13px;
            cursor: pointer;
            margin-top: 16px;
        }

        .reset-button:hover {
            background: #edebe9;
        }

        .examples-panel {
            padding: 20px;
        }

        .example-item {
            padding: 12px;
            background: #f8f9fa;
            border: 1px solid #e1e1e1;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 8px;
            transition: all 0.2s;
        }

        .example-item:hover {
            background: #e1e1e1;
            border-color: #0078d4;
        }

        .example-title {
            font-weight: 600;
            color: #0078d4;
            font-size: 12px;
            margin-bottom: 4px;
        }

        .example-text {
            font-size: 11px;
            color: #605e5c;
            line-height: 1.4;
        }

        /* Main chat area */
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: white;
        }

        .chat-header {
            padding: 16px 24px;
            border-bottom: 1px solid #e0e0e0;
            background: #f8f9fa;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px 24px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .message {
            max-width: 80%;
            padding: 16px;
            border-radius: 12px;
            font-size: 14px;
            line-height: 1.5;
        }

        .message.user {
            background: #0078d4;
            color: white;
            align-self: flex-end;
            border-bottom-right-radius: 4px;
        }

        .message.assistant {
            background: #f1f3f4;
            color: #323130;
            align-self: flex-start;
            border-bottom-left-radius: 4px;
            border: 1px solid #e1e1e1;
        }

        .message-content {
            margin-bottom: 8px;
        }

        .message-code {
            background: #2d2d2d;
            color: #f8f8f8;
            padding: 16px;
            border-radius: 6px;
            overflow-x: auto;
            font-family: 'Consolas', 'Courier New', monospace;
            font-size: 13px;
            white-space: pre-wrap;
            margin: 12px 0;
        }

        .message-stats {
            font-size: 11px;
            color: #8a8886;
            margin-top: 8px;
            padding-top: 8px;
            border-top: 1px solid rgba(0,0,0,0.1);
        }

        .chat-input-area {
            padding: 16px 24px;
            border-top: 1px solid #e0e0e0;
            background: #f8f9fa;
        }

        .input-container {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .chat-input {
            flex: 1;
            min-height: 40px;
            max-height: 120px;
            padding: 12px;
            border: 2px solid #e1e1e1;
            border-radius: 20px;
            font-family: inherit;
            font-size: 14px;
            resize: none;
            outline: none;
        }

        .chat-input:focus {
            border-color: #0078d4;
        }

        .send-button {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: #0078d4;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.2s;
        }

        .send-button:hover {
            background: #106ebe;
        }

        .send-button:disabled {
            background: #c8c8c8;
            cursor: not-allowed;
        }

        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #0078d4;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #8a8886;
            font-size: 16px;
        }

        .copy-button {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(0,0,0,0.6);
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .message.assistant:hover .copy-button {
            opacity: 1;
        }

        .message.assistant {
            position: relative;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h1>🌟 Code Morningstar</h1>
                <div class="status-indicator">
                    <div class="status-dot"></div>
                    <span id="status-text">Connecting...</span>
                </div>
            </div>

            <!-- Prompt dropdown removed as requested -->

            <div class="settings-panel">
                <div class="settings-title">⚙️ Advanced Settings</div>
                <div class="setting-item">
                    <label class="setting-label">Max Tokens: <span id="max-tokens-value">2048</span></label>
                    <input type="range" id="max-tokens" class="slider" min="1" max="8192" value="2048">
                </div>
                <div class="setting-item">
                    <label class="setting-label">Temperature: <span id="temperature-value">0.7</span></label>
                    <input type="range" id="temperature" class="slider" min="0" max="2" step="0.1" value="0.7">
                </div>
                <div class="setting-item">
                    <label class="setting-label">Top P: <span id="top-p-value">0.9</span></label>
                    <input type="range" id="top-p" class="slider" min="0" max="1" step="0.01" value="0.9">
                </div>
                <div class="setting-item">
                    <label class="setting-label">Top K: <span id="top-k-value">40</span></label>
                    <input type="range" id="top-k" class="slider" min="1" max="100" value="40">
                </div>
                <div class="setting-item">
                    <label class="setting-label">Repeat Penalty: <span id="repeat-penalty-value">1.1</span></label>
                    <input type="range" id="repeat-penalty" class="slider" min="0" max="2" step="0.1" value="1.1">
                </div>
                <div class="setting-item">
                    <label class="setting-label">Frequency Penalty: <span id="frequency-penalty-value">0.0</span></label>
                    <input type="range" id="frequency-penalty" class="slider" min="-2" max="2" step="0.1" value="0">
                </div>
                <div class="setting-item">
                    <label class="setting-label">Presence Penalty: <span id="presence-penalty-value">0.0</span></label>
                    <input type="range" id="presence-penalty" class="slider" min="-2" max="2" step="0.1" value="0">
                </div>
                <button id="reset-defaults" class="reset-button">🔄 Reset to Defaults</button>
            </div>

            <div class="examples-panel">
                <div class="settings-title">💡 Examples</div>
                <div id="examples-container">
                    <!-- Examples will be populated here -->
                </div>
            </div>
        </div>

        <!-- Main Chat Area -->
        <div class="chat-container">
            <div class="chat-header">
                <h2>AI Code Assistant</h2>
                <p>Ask me to generate any code, explain concepts, or help with programming tasks!</p>
            </div>

            <div class="chat-messages" id="chat-messages">
                <div class="empty-state">
                    <div style="font-size: 48px; margin-bottom: 16px;">💬</div>
                    <div>Start a conversation by typing a message below</div>
                    <div style="font-size: 14px; margin-top: 8px; opacity: 0.7;">Try asking me to write some code!</div>
                </div>
            </div>

            <div class="chat-input-area">
                <div style="display: flex; flex-direction: column; gap: 8px; width: 100%;">
                    <textarea 
                        id="system-prompt"
                        class="chat-input"
                        style="min-height:32px; max-height:64px; border-radius:12px; border:1.5px solid #e1e1e1; font-size:13px;"
                        placeholder="System prompt (instructions for the AI, optional)"
                        rows="2"
                    ></textarea>
                    <div class="input-container">
                        <textarea 
                            id="chat-input" 
                            class="chat-input" 
                            placeholder="Ask me to write some code... (Shift+Enter for new line, Enter to send)"
                            rows="1"
                        ></textarea>
                        <button id="send-button" class="send-button">
                            🚀
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Example prompts
        const examples = [
            { title: "Python Function", prompt: "Write a Python function to calculate the factorial of a number using recursion." },
            { title: "JavaScript API", prompt: "Create a JavaScript function that fetches data from a REST API and handles errors." },
            { title: "SQL Query", prompt: "Write a SQL query to find the top 5 customers by total purchase amount." },
            { title: "React Component", prompt: "Create a React component for a simple todo list with add and delete functionality." },
            { title: "CSS Animation", prompt: "Write CSS code for a smooth fade-in animation that triggers on page load." },
            { title: "REST API", prompt: "Create a Flask REST API endpoint that handles user authentication with JWT tokens." },
            { title: "Database Schema", prompt: "Design a SQL database schema for an e-commerce platform with products, orders, and customers." },
            { title: "Algorithm", prompt: "Implement a binary search algorithm in Python with detailed comments." }
        ];

        let conversationHistory = [];
        let isLoading = false;
        let defaultParameters = {};

        // DOM elements
        const chatMessages = document.getElementById('chat-messages');
        const chatInput = document.getElementById('chat-input');
        const sendButton = document.getElementById('send-button');
        const statusText = document.getElementById('status-text');
        const examplesContainer = document.getElementById('examples-container');
        const resetButton = document.getElementById('reset-defaults');

        // Slider elements
        const sliders = {
            'max-tokens': { element: null, valueDisplay: null },
            'temperature': { element: null, valueDisplay: null },
            'top-p': { element: null, valueDisplay: null },
            'top-k': { element: null, valueDisplay: null },
            'repeat-penalty': { element: null, valueDisplay: null },
            'frequency-penalty': { element: null, valueDisplay: null },
            'presence-penalty': { element: null, valueDisplay: null }
        };

        // Initialize slider references
        function initializeSliderReferences() {
            Object.keys(sliders).forEach(sliderId => {
                sliders[sliderId].element = document.getElementById(sliderId);
                sliders[sliderId].valueDisplay = document.getElementById(sliderId + '-value');
            });
        }

        // Initialize the app
        async function initialize() {
            initializeSliderReferences();
            await loadDefaultParameters();
            await checkStatus();
            populateExamples();
            setupEventListeners();
            // Auto-resize textarea
            setupTextareaAutoResize();
        }

        // Load default parameters
        async function loadDefaultParameters() {
            try {
                const response = await fetch('http://localhost:8000/llm/parameters');
                defaultParameters = await response.json();
            } catch (error) {
                console.error('Failed to load default parameters:', error);
                defaultParameters = {
                    temperature: {default: 0.7},
                    max_tokens: {default: 2048},
                    top_p: {default: 0.9},
                    top_k: {default: 40},
                    repeat_penalty: {default: 1.1},
                    frequency_penalty: {default: 0.0},
                    presence_penalty: {default: 0.0}
                };
            }
        }

        // Check backend status
        async function checkStatus() {
            try {
                const response = await fetch('http://localhost:8000/llm/health');
                const data = await response.json();
                
                if (data.status === 'healthy') {
                    statusText.textContent = data.model_loaded ? 'Model Ready' : 'Model Not Loaded';
                    document.querySelector('.status-dot').style.background = data.model_loaded ? '#00ff00' : '#ff0000';
                } else {
                    statusText.textContent = 'Service Issue';
                    document.querySelector('.status-dot').style.background = '#ff8800';
                }
            } catch (error) {
                statusText.textContent = 'Connection Error';
                document.querySelector('.status-dot').style.background = '#ff0000';
            }
        }

        // Populate examples
        function populateExamples() {
            examplesContainer.innerHTML = examples.map(example => `
                <div class="example-item" onclick="loadExample('${example.prompt.replace(/'/g, "\\'")}')">
                    <div class="example-title">${example.title}</div>
                    <div class="example-text">${example.prompt.substring(0, 60)}...</div>
                </div>
            `).join('');
        }

        // Populate prompt dropdown
        // Prompt dropdown removed

        // Load example
        function loadExample(prompt) {
            chatInput.value = prompt;
            chatInput.focus();
        }

        // Setup event listeners
        function setupEventListeners() {
            sendButton.addEventListener('click', sendMessage);
            resetButton.addEventListener('click', resetToDefaults);

            chatInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // Prompt dropdown event removed

            // Setup slider listeners
            Object.keys(sliders).forEach(sliderId => {
                const slider = sliders[sliderId].element;
                if (slider) {
                    slider.addEventListener('input', () => updateSliderValue(sliderId));
                    updateSliderValue(sliderId);
                }
            });
        }

        // Setup textarea auto-resize
        function setupTextareaAutoResize() {
            chatInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 120) + 'px';
            });
        }

        // Update slider value
        function updateSliderValue(sliderId) {
            const slider = sliders[sliderId];
            if (slider.element && slider.valueDisplay) {
                const value = parseFloat(slider.element.value);
                slider.valueDisplay.textContent = value % 1 === 0 ? value.toString() : value.toFixed(2);
            }
        }

        // Reset to defaults
        function resetToDefaults() {
            Object.keys(sliders).forEach(sliderId => {
                const paramName = sliderId.replace('-', '_');
                const defaultValue = defaultParameters[paramName]?.default;
                if (defaultValue !== undefined && sliders[sliderId].element) {
                    sliders[sliderId].element.value = defaultValue;
                    updateSliderValue(sliderId);
                }
            });
        }

        // Get current parameters
        function getCurrentParameters() {
            const params = { 
                prompt: chatInput.value.trim(),
                system_prompt: document.getElementById('system-prompt').value.trim(),
                history: conversationHistory.map(item => ({
                    user: item.user,
                    assistant: item.assistant
                }))
            };
            Object.keys(sliders).forEach(sliderId => {
                const slider = sliders[sliderId].element;
                if (slider) {
                    const paramName = sliderId.replace('-', '_');
                    let value = slider.value;
                    if (sliderId === 'max-tokens' || sliderId === 'top-k') {
                        params[paramName] = parseInt(value);
                    } else {
                        params[paramName] = parseFloat(value);
                    }
                }
            });
            return params;
        }

        // Add message to chat
        function addMessage(content, isUser = false, stats = null) {
            // Remove empty state if it exists
            const emptyState = document.querySelector('.empty-state');
            if (emptyState) {
                emptyState.remove();
            }

            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user' : 'assistant'}`;
            
            if (isUser) {
                messageDiv.innerHTML = `<div class="message-content">${content}</div>`;
            } else {
                const hasCode = content.includes('```') || content.includes('function') || content.includes('class') || content.includes('def ');
                
                let messageContent = content;
                if (hasCode && !content.includes('<pre')) {
                    messageContent = `<div class="message-code">${content}</div>`;
                } else {
                    messageContent = `<div class="message-content">${content.replace(/\n/g, '<br>')}</div>`;
                }

                let statsHtml = '';
                if (stats) {
                    statsHtml = `
                        <div class="message-stats">
                            📊 ${stats.tokens || 0} tokens • ⏱️ ${stats.time ? (stats.time * 1000).toFixed(0) + 'ms' : 'N/A'} • 🌡️ ${stats.temperature || 'N/A'}
                        </div>
                    `;
                }

                messageDiv.innerHTML = `
                    ${messageContent}
                    ${statsHtml}
                    <button class="copy-button" onclick="copyMessage(this)">📋</button>
                `;
            }

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Copy message content
        function copyMessage(button) {
            const message = button.closest('.message');
            const codeElement = message.querySelector('.message-code');
            const textToCopy = codeElement ? codeElement.textContent : message.querySelector('.message-content').textContent;
            
            navigator.clipboard.writeText(textToCopy).then(() => {
                button.textContent = '✅';
                setTimeout(() => {
                    button.textContent = '📋';
                }, 2000);
            });
        }

        // Send message
        async function sendMessage() {
            if (!chatInput.value.trim() || isLoading) return;

            const params = getCurrentParameters();
            const userMessage = params.prompt;
            
            // Add user message
            addMessage(userMessage, true);
            
            // Clear input
            chatInput.value = '';
            chatInput.style.height = 'auto';
            
            // Show loading
            isLoading = true;
            sendButton.innerHTML = '<div class="loading-spinner"></div>';
            sendButton.disabled = true;

            try {
                const response = await fetch('http://localhost:8000/llm/generate', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(params)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();
                
                // Add assistant message
                addMessage(data.result || 'No response received', false, {
                    tokens: data.tokens_used,
                    time: data.processing_time,
                    temperature: params.temperature
                });

                // Add to history
                conversationHistory.push({
                    user: userMessage,
                    assistant: data.result,
                    parameters: params,
                    stats: { tokens: data.tokens_used, time: data.processing_time }
                });

            } catch (error) {
                addMessage(`❌ Error: ${error.message}`, false);
            } finally {
                isLoading = false;
                sendButton.innerHTML = '🚀';
                sendButton.disabled = false;
                chatInput.focus();
            }
        }

        // Make copyMessage available globally
        window.copyMessage = copyMessage;

        // Initialize the app
        initialize();
    </script>
</body>
</html>
