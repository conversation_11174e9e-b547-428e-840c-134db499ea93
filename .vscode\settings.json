{"github.copilot.nextEditSuggestions.enabled": true, "C_Cpp.autocompleteAddParentheses": false, "github.copilot.chat.languageContext.fix.typescript.enabled": true, "github.copilot.chat.languageContext.inline.typescript.enabled": true, "github.copilot.chat.languageContext.typescript.enabled": true, "go.coverOnSave": true, "go.coverShowCounts": true, "go.editorContextMenuCommands": {"removeTags": true, "fillStruct": true, "testFile": true, "testPackage": true, "generateTestForFile": true, "generateTestForPackage": true, "benchmarkAtCursor": true}, "go.toolsManagement.autoUpdate": true, "debugpy.showPythonInlineValues": true, "python.testing.unittestArgs": ["-v", "-s", ".", "-p", "*test.py"], "python.testing.pytestEnabled": false, "python.testing.unittestEnabled": true}