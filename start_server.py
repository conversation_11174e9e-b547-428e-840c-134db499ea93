#!/usr/bin/env python3
"""
Code Morningstar Server Launcher
Run this from the root Code-Morningstar directory
"""

import uvicorn
import os
import sys
from pathlib import Path

def main():
    # Ensure we're in the right directory
    root_dir = Path(__file__).parent
    os.chdir(root_dir)
    
    # Add the root directory to Python path
    sys.path.insert(0, str(root_dir))
    
    print("?? Starting Code Morningstar API Server...")
    print(f"?? Working directory: {root_dir}")
    print(f"?? API will be available at: http://localhost:8000")
    print(f"?? API docs will be available at: http://localhost:8000/docs")
    print(f"?? Health check: http://localhost:8000/llm/health")
    
    # Start the server
    uvicorn.run(
        "backend.app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        reload_dirs=["backend"],
        log_level="info"
    )

if __name__ == "__main__":
    main()