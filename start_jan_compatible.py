#!/usr/bin/env python3
"""
Start Code Morningstar with Jan.ai Compatible API
Runs both the main API and OpenAI-compatible API for Jan.ai integration
"""

import subprocess
import sys
import time
import threading
from pathlib import Path
import requests
import signal
import atexit

class CodeMorningstarJanLauncher:
    def __init__(self):
        self.main_process = None
        self.openai_process = None
        self.backend_path = Path("backend")
        
        # Register cleanup
        atexit.register(self.cleanup)
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
    def start_main_api(self):
        """Start the main Code Morningstar API"""
        print("🚀 Starting Code Morningstar Main API (port 8002)...")
        
        try:
            self.main_process = subprocess.Popen(
                [sys.executable, "main.py"],
                cwd=self.backend_path,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True
            )
            
            # Monitor main API output
            def monitor_main():
                for line in iter(self.main_process.stdout.readline, ''):
                    if line:
                        print(f"[MAIN] {line.strip()}")
                        
            threading.Thread(target=monitor_main, daemon=True).start()
            
        except Exception as e:
            print(f"❌ Failed to start main API: {e}")
            return False
            
        return True
        
    def start_openai_api(self):
        """Start the OpenAI-compatible API"""
        print("🔗 Starting OpenAI-Compatible API for Jan.ai (port 8001)...")
        
        try:
            self.openai_process = subprocess.Popen(
                [sys.executable, "openai_compatible_api.py"],
                cwd=self.backend_path,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True
            )
            
            # Monitor OpenAI API output
            def monitor_openai():
                for line in iter(self.openai_process.stdout.readline, ''):
                    if line:
                        print(f"[OPENAI] {line.strip()}")
                        
            threading.Thread(target=monitor_openai, daemon=True).start()
            
        except Exception as e:
            print(f"❌ Failed to start OpenAI API: {e}")
            return False
            
        return True
        
    def wait_for_apis(self):
        """Wait for both APIs to be ready"""
        print("⏳ Waiting for APIs to start...")
        
        # Wait for main API
        for i in range(30):
            try:
                response = requests.get("http://localhost:8002/llm/health", timeout=2)
                if response.status_code == 200:
                    print("✅ Main API ready!")
                    break
            except:
                pass
            time.sleep(2)
        else:
            print("❌ Main API failed to start")
            return False
            
        # Wait for OpenAI API
        for i in range(10):
            try:
                response = requests.get("http://localhost:8001/health", timeout=2)
                if response.status_code == 200:
                    print("✅ OpenAI-Compatible API ready!")
                    break
            except:
                pass
            time.sleep(1)
        else:
            print("❌ OpenAI API failed to start")
            return False
            
        return True
        
    def print_setup_instructions(self):
        """Print Jan.ai setup instructions"""
        print("\n" + "="*60)
        print("🌟 CODE MORNINGSTAR + JAN.AI SETUP COMPLETE!")
        print("="*60)
        print()
        print("📋 JAN.AI CONFIGURATION:")
        print("   1. Open Jan.ai application")
        print("   2. Go to Settings > Models")
        print("   3. Add Custom Model with these settings:")
        print()
        print("   📡 API Base URL: http://localhost:8001")
        print("   🤖 Model ID: code-morningstar-34b")
        print("   🔑 API Key: (leave empty or use 'not-required')")
        print("   📝 Model Name: Code Morningstar 34B")
        print()
        print("🎯 FEATURES YOU'LL GET:")
        print("   • ChatGPT-style interface")
        print("   • Professional code generation")
        print("   • 34B parameter intelligence")
        print("   • Maximum precision responses")
        print("   • Local privacy (no data sent to cloud)")
        print()
        print("🔗 DIRECT LINKS:")
        print("   • Main API: http://localhost:8002/docs")
        print("   • Jan.ai API: http://localhost:8001")
        print("   • Health Check: http://localhost:8001/health")
        print("   • Your API Tower: http://localhost:8000 (unchanged)")
        print()
        print("💡 TIP: In Jan.ai, start a new chat and select 'Code Morningstar 34B'")
        print("="*60)
        
    def run(self):
        """Run both APIs"""
        print("🌟 Code Morningstar + Jan.ai Integration Launcher")
        print("=" * 50)
        
        # Check backend directory
        if not self.backend_path.exists():
            print("❌ Backend directory not found!")
            print("Please run this from the Code-Morningstar root directory.")
            return
            
        # Start main API
        if not self.start_main_api():
            return
            
        # Wait a bit for main API to initialize
        time.sleep(5)
        
        # Start OpenAI-compatible API
        if not self.start_openai_api():
            return
            
        # Wait for both APIs to be ready
        if not self.wait_for_apis():
            return
            
        # Print setup instructions
        self.print_setup_instructions()
        
        # Keep running
        try:
            print("\n🔄 Both APIs are running. Press Ctrl+C to stop.")
            while True:
                time.sleep(1)
                
                # Check if processes are still running
                if self.main_process and self.main_process.poll() is not None:
                    print("❌ Main API process died")
                    break
                    
                if self.openai_process and self.openai_process.poll() is not None:
                    print("❌ OpenAI API process died")
                    break
                    
        except KeyboardInterrupt:
            print("\n🛑 Shutting down...")
            
        self.cleanup()
        
    def cleanup(self):
        """Clean up processes"""
        if self.main_process:
            print("🛑 Stopping main API...")
            self.main_process.terminate()
            self.main_process = None
            
        if self.openai_process:
            print("🛑 Stopping OpenAI API...")
            self.openai_process.terminate()
            self.openai_process = None
            
    def signal_handler(self, signum, frame):
        """Handle signals"""
        print(f"\n🛑 Received signal {signum}, shutting down...")
        self.cleanup()
        sys.exit(0)

def main():
    launcher = CodeMorningstarJanLauncher()
    launcher.run()

if __name__ == "__main__":
    main()
