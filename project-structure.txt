C:\Users\<USER>\Code-Morningstar\.env.example
C:\Users\<USER>\Code-Morningstar\all-files.txt
C:\Users\<USER>\Code-Morningstar\CHANGELOG.md
C:\Users\<USER>\Code-Morningstar\Clean CAN.md
C:\Users\<USER>\Code-Morningstar\Code-Morningstar
C:\Users\<USER>\Code-Morningstar\Code-Morningstar.code-workspace
C:\Users\<USER>\Code-Morningstar\Code-Morningstar.ps1
C:\Users\<USER>\Code-Morningstar\CONTRIBUTING.md
C:\Users\<USER>\Code-Morningstar\debug_test.py
C:\Users\<USER>\Code-Morningstar\deploy.bat
C:\Users\<USER>\Code-Morningstar\deploy.py
C:\Users\<USER>\Code-Morningstar\deploy.sh
C:\Users\<USER>\Code-Morningstar\environment.yml
C:\Users\<USER>\Code-Morningstar\LICENSE
C:\Users\<USER>\Code-Morningstar\package-lock.json
C:\Users\<USER>\Code-Morningstar\package.json
C:\Users\<USER>\Code-Morningstar\README.md
C:\Users\<USER>\Code-Morningstar\restore_project.log
C:\Users\<USER>\Code-Morningstar\SECURITY.md
C:\Users\<USER>\Code-Morningstar\start_server.py
C:\Users\<USER>\Code-Morningstar\ADRs\0001-architecture.md
C:\Users\<USER>\Code-Morningstar\backend\__init__.py
C:\Users\<USER>\Code-Morningstar\backend\.env
C:\Users\<USER>\Code-Morningstar\backend\.env.example
C:\Users\<USER>\Code-Morningstar\backend\Dockerfile
C:\Users\<USER>\Code-Morningstar\backend\pyproject.toml
C:\Users\<USER>\Code-Morningstar\backend\pytest.ini
C:\Users\<USER>\Code-Morningstar\backend\requirements.txt
C:\Users\<USER>\Code-Morningstar\backend\settings.py
C:\Users\<USER>\Code-Morningstar\backend\start.py
C:\Users\<USER>\Code-Morningstar\backend\app\__init__.py
C:\Users\<USER>\Code-Morningstar\backend\app\api_router.py
C:\Users\<USER>\Code-Morningstar\backend\app\llm_api.py
C:\Users\<USER>\Code-Morningstar\backend\app\main.py
C:\Users\<USER>\Code-Morningstar\backend\db\__init__.py
C:\Users\<USER>\Code-Morningstar\backend\db\model.py
C:\Users\<USER>\Code-Morningstar\backend\feature_flags\__init__.py
C:\Users\<USER>\Code-Morningstar\backend\feature_flags\flags.yaml
C:\Users\<USER>\Code-Morningstar\backend\feature_flags\manager.py
C:\Users\<USER>\Code-Morningstar\backend\models\.cache\huggingface\download\codellama-70b-instruct.Q5_K_M.gguf.metadata
C:\Users\<USER>\Code-Morningstar\backend\services\__init__.py
C:\Users\<USER>\Code-Morningstar\backend\services\cassandra_service.py
C:\Users\<USER>\Code-Morningstar\backend\services\db_router.py
C:\Users\<USER>\Code-Morningstar\backend\services\elasticsearch_service.py
C:\Users\<USER>\Code-Morningstar\backend\services\llm_service.py
C:\Users\<USER>\Code-Morningstar\backend\services\mongo_service.py
C:\Users\<USER>\Code-Morningstar\backend\services\mysql_service.py
C:\Users\<USER>\Code-Morningstar\backend\services\neo4j_service.py
C:\Users\<USER>\Code-Morningstar\backend\services\postgres_service.py
C:\Users\<USER>\Code-Morningstar\backend\services\redis_service.py
C:\Users\<USER>\Code-Morningstar\backend\services\sqlite_service.py
C:\Users\<USER>\Code-Morningstar\backend\tests\__init__.py
C:\Users\<USER>\Code-Morningstar\backend\tests\test_db_services.py
C:\Users\<USER>\Code-Morningstar\backend\tests\test_llm_service.py
C:\Users\<USER>\Code-Morningstar\docs\API.md
C:\Users\<USER>\Code-Morningstar\docs\DEVELOPMENT.md
C:\Users\<USER>\Code-Morningstar\docs\README.md
C:\Users\<USER>\Code-Morningstar\frontend\Dockerfile
C:\Users\<USER>\Code-Morningstar\frontend\index.html
C:\Users\<USER>\Code-Morningstar\frontend\package_new.json
C:\Users\<USER>\Code-Morningstar\frontend\package.json
C:\Users\<USER>\Code-Morningstar\frontend\standalone.html
C:\Users\<USER>\Code-Morningstar\frontend\svelte.config.js
C:\Users\<USER>\Code-Morningstar\frontend\tsconfig.json
C:\Users\<USER>\Code-Morningstar\frontend\vite.config.js
C:\Users\<USER>\Code-Morningstar\frontend\windows-ui.html
C:\Users\<USER>\Code-Morningstar\frontend\public\vite.svg
C:\Users\<USER>\Code-Morningstar\frontend\src\App.svelte
C:\Users\<USER>\Code-Morningstar\frontend\src\main.ts
C:\Users\<USER>\Code-Morningstar\frontend\src\lib\Message.svelte
C:\Users\<USER>\Code-Morningstar\frontend\src\lib\prompts.ts
C:\Users\<USER>\Code-Morningstar\infra\k8s\backend-deployment.yaml
C:\Users\<USER>\Code-Morningstar\infra\k8s\backend-service.yaml
C:\Users\<USER>\Code-Morningstar\infra\k8s\frontend-deployment.yaml
C:\Users\<USER>\Code-Morningstar\infra\k8s\frontend-service.yaml
C:\Users\<USER>\Code-Morningstar\infra\terraform\main.tf
C:\Users\<USER>\Code-Morningstar\logs\setup.log
