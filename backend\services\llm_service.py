import logging
from pathlib import Path
import asyncio
from concurrent.futures import ThreadPoolExecutor
from typing import Optional
try:
    from .code_analyzer import CodeAnalyzer
except ImportError:
    from code_analyzer import CodeAnalyzer

logger = logging.getLogger(__name__)

class LLMService:
    def __init__(self, model_path: str):
        self.model_path = Path(model_path)
        self.model = None
        self.executor = ThreadPoolExecutor(max_workers=1)
        self.code_analyzer = CodeAnalyzer()  # Advanced code analysis
        self._load_model()
    
    def _load_model(self):
        try:
            if not self.model_path.exists():
                raise FileNotFoundError(f"Model file not found: {self.model_path}")

            logger.info(f"Loading CodeLlama model from {self.model_path}")

            try:
                from llama_cpp import Llama
            except ImportError:
                raise ImportError("llama-cpp-python is not installed. Please install it with: pip install llama-cpp-python")

            self.model = Llama(
                model_path=str(self.model_path),
                n_ctx=8192,  # Increased context for better understanding
                n_batch=1024,  # Larger batch for better throughput
                n_threads=8,
                n_gpu_layers=50,  # Maximum GPU utilization for RTX 4070
                verbose=False,
                seed=-1,
                f16_kv=True,
                use_mlock=False,  # Better for larger models
                use_mmap=True,
                # Maximum performance optimizations
                n_parts=1,
                rope_freq_base=10000.0,
                rope_freq_scale=1.0,
                mul_mat_q=True,
                logits_all=False,
                embedding=False,
                offload_kqv=True,
                flash_attn=True,
                # Additional accuracy optimizations
                numa=True,  # NUMA optimization
                low_vram=False,  # We have enough VRAM
                tensor_split=None,  # Single GPU optimization
                main_gpu=0,  # Use primary GPU
                split_mode=1  # Layer split mode
            )
            logger.info("✅ CodeLlama 34B model loaded successfully")

            # Warm up the model with a small prompt
            try:
                logger.info("🔥 Warming up model...")
                _ = self.model(
                    "<s>[INST] Hello [/INST]",
                    max_tokens=5,
                    temperature=0.1,
                    stream=False
                )
                logger.info("✅ Model warmed up successfully")
            except Exception as e:
                logger.warning(f"Model warmup failed (non-critical): {e}")

        except Exception as e:
            logger.error(f"Model initialization failed: {e}")
            raise
    
    async def generate_async(self, prompt: str, max_tokens: int = 4096, temperature: float = 0.1,
                            code_context: Optional[str] = None) -> str:
        """Generate text asynchronously with maximum intelligence."""
        # Enhance prompt with code analysis if context provided
        if code_context:
            try:
                enhanced_prompt = self.code_analyzer.enhance_prompt_with_context(prompt, code_context)
            except:
                enhanced_prompt = prompt  # Fallback to original prompt
        else:
            enhanced_prompt = prompt

        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.executor,
            self._generate_sync,
            enhanced_prompt,
            max_tokens,
            temperature
        )
    
    def _generate_sync(self, prompt: str, max_tokens: int, temperature: float, stream: bool = False):
        if not prompt.strip():
            raise ValueError("Prompt cannot be empty")

        if self.model is None:
            raise RuntimeError("Model is not loaded. Please ensure the model file exists and is properly configured.")

        try:
            # Advanced prompt engineering for maximum intelligence
            system_prompt = """You are an expert software engineer with deep knowledge of algorithms, design patterns, and best practices. You write clean, efficient, and well-documented code. Always consider edge cases, performance implications, and maintainability."""

            formatted_prompt = f"""<s>[INST] <<SYS>>
{system_prompt}
<</SYS>>

{prompt} [/INST]"""

            response = self.model(
                formatted_prompt,
                max_tokens=max_tokens,
                temperature=temperature,
                top_p=0.95,  # Higher precision sampling
                top_k=50,    # Focused vocabulary selection
                repeat_penalty=1.05,  # Reduced for more natural code
                frequency_penalty=0.1,  # Encourage diverse vocabulary
                presence_penalty=0.1,   # Encourage new concepts
                stop=["</s>", "[INST]", "[/INST]", "Human:", "Assistant:"],
                echo=False,
                stream=stream,
                # Advanced sampling for maximum accuracy
                mirostat_mode=2,      # Enable mirostat sampling
                mirostat_tau=5.0,     # Target entropy
                mirostat_eta=0.1,     # Learning rate
                typical_p=1.0,        # Typical sampling
                tfs_z=1.0            # Tail free sampling
            )

            # Handle llama-cpp-python response format
            if isinstance(response, dict):
                # Standard response format
                if 'choices' in response and response['choices']:
                    return response['choices'][0].get('text', '').strip()
                return str(response).strip()
            else:
                # Fallback for other formats
                return str(response).strip()

        except Exception as e:
            logger.error(f"Generation failed: {e}")
            raise RuntimeError(f"Generation failed: {str(e)}")
    
    def generate(self, prompt: str, max_tokens: int = 512, temperature: float = 0.7) -> str:
        return self._generate_sync(prompt, max_tokens, temperature)
    
    def is_model_loaded(self) -> bool:
        return self.model is not None
    
    def get_model_info(self) -> dict:
        return {
            "model_path": str(self.model_path),
            "model_loaded": self.is_model_loaded(),
            "model_exists": self.model_path.exists() if self.model_path else False,
            "model_size_gb": round(self.model_path.stat().st_size / (1024**3), 1) if self.model_path.exists() else 0
        }
