import sys
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from fastapi import FastAPI, HTTPException, Header
from fastapi.middleware.cors import CORSMiddleware
from typing import Optional
from pydantic import BaseModel, <PERSON>
from typing import Optional, Dict, Any
import uvicorn
import logging
import time
import traceback
from settings import get_settings
from services.llm_service import LLMService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Code Morningstar",
    description="Enterprise AI Code Generation Platform",
    version="2.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request/Response Models
class GenerateRequest(BaseModel):
    prompt: str = Field(..., min_length=1, max_length=8192, description="The prompt to generate code from")
    temperature: Optional[float] = Field(0.7, ge=0.0, le=2.0, description="Sampling temperature (0.0-2.0)")
    max_tokens: Optional[int] = Field(2048, ge=1, le=8192, description="Maximum tokens to generate")
    top_p: Optional[float] = Field(0.9, ge=0.0, le=1.0, description="Nucleus sampling parameter")
    top_k: Optional[int] = Field(40, ge=1, le=100, description="Top-k sampling parameter")
    repeat_penalty: Optional[float] = Field(1.1, ge=0.0, le=2.0, description="Repetition penalty")
    frequency_penalty: Optional[float] = Field(0.0, ge=-2.0, le=2.0, description="Frequency penalty")
    presence_penalty: Optional[float] = Field(0.0, ge=-2.0, le=2.0, description="Presence penalty")

class GenerateResponse(BaseModel):
    model_config = {"protected_namespaces": ()}

    result: str
    tokens_used: int
    processing_time: float
    model_info: Dict[str, Any]

class HealthResponse(BaseModel):
    model_config = {"protected_namespaces": ()}

    status: str
    model_loaded: bool
    model_path: str
    model_exists: bool
    settings: Dict[str, Any]

# Real LLM Service
class CodeMorningstarLLMService:
    def __init__(self):
        self.settings = get_settings()
        self.llm_service = LLMService(str(self.settings.llm_model_path))
        
    def generate(self, request: GenerateRequest) -> GenerateResponse:
        import time
        start_time = time.time()
        
        try:
            # Use the real LLM service (with parameters it supports)
            result = self.llm_service.generate(
                prompt=request.prompt,
                max_tokens=request.max_tokens or 2048,
                temperature=request.temperature or 0.7
            )
            
            processing_time = time.time() - start_time
            
            return GenerateResponse(
                result=result,
                tokens_used=len(result.split()),
                processing_time=processing_time,
                model_info={
                    "model_name": "CodeLlama-70B",
                    "model_path": str(self.settings.llm_model_path),
                    "model_loaded": self.llm_service.is_model_loaded(),
                    "temperature": request.temperature,
                    "max_tokens": request.max_tokens,
                    "top_p": request.top_p,
                    "top_k": request.top_k,
                    "repeat_penalty": request.repeat_penalty,
                    "frequency_penalty": request.frequency_penalty,
                    "presence_penalty": request.presence_penalty
                }
            )
            
        except Exception as e:
            logger.error(f"Generation failed: {e}")
            processing_time = time.time() - start_time
            raise HTTPException(
                status_code=500,
                detail=f"Generation failed: {str(e)}. Please ensure the model is properly loaded and configured."
            )
    
    def health_check(self) -> HealthResponse:
        settings = get_settings()
        model_path = settings.llm_model_path
        model_exists = Path(model_path).exists() if model_path else False
        model_loaded = hasattr(self.llm_service, 'model') and self.llm_service.model is not None
        
        return HealthResponse(
            status="healthy",
            model_loaded=model_loaded,
            model_path=model_path,
            model_exists=model_exists,
            settings={
                "temperature": settings.temperature,
                "max_tokens": settings.max_tokens,
                "context_length": settings.context_length,
                "debug_mode": settings.debug_mode
            }
        )

# Initialize the LLM service
llm_service = CodeMorningstarLLMService()

@app.get("/")
def root():
    return {
        "message": "🌟 Code Morningstar v2.0 is running!",
        "status": "healthy",
        "description": "Enterprise AI Code Generation Platform",
        "version": "2.0.0",
        "features": [
            "Advanced parameter tuning",
            "Multiple sampling methods",
            "Real-time adjustments",
            "Health monitoring"
        ]
    }

@app.post("/llm/generate", response_model=GenerateResponse)
def generate_text(request: GenerateRequest, authorization: Optional[str] = Header(None)):
    """Generate text/code with advanced parameter controls"""
    try:
        logger.info(f"Generating text with params: temp={request.temperature}, tokens={request.max_tokens}")
        result = llm_service.generate(request)
        return result
    except Exception as ex:
        logger.error(f"Generation failed: {ex}")
        raise HTTPException(status_code=500, detail=f"Generation failed: {str(ex)}")

@app.get("/llm/health", response_model=HealthResponse)
def health_check():
    """Health check endpoint for LLM service with detailed info"""
    try:
        return llm_service.health_check()
    except Exception as ex:
        logger.error(f"Health check failed: {ex}")
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(ex)}")

@app.get("/llm/parameters")
def get_default_parameters():
    """Get default parameter ranges for the UI sliders"""
    return {
        "temperature": {"min": 0.0, "max": 2.0, "default": 0.7, "step": 0.1},
        "max_tokens": {"min": 1, "max": 8192, "default": 2048, "step": 1},
        "top_p": {"min": 0.0, "max": 1.0, "default": 0.9, "step": 0.01},
        "top_k": {"min": 1, "max": 100, "default": 40, "step": 1},
        "repeat_penalty": {"min": 0.0, "max": 2.0, "default": 1.1, "step": 0.1},
        "frequency_penalty": {"min": -2.0, "max": 2.0, "default": 0.0, "step": 0.1},
        "presence_penalty": {"min": -2.0, "max": 2.0, "default": 0.0, "step": 0.1}
    }

if __name__ == "__main__":
    settings = get_settings()
    logger.info("🚀 Starting Code Morningstar Backend v2.0")
    uvicorn.run(
        app, 
        host=settings.host, 
        port=settings.port,
        log_level=settings.log_level.lower()
    )
