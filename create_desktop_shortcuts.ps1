$desktop = [Environment]::GetFolderPath('Desktop')
$wshell = New-Object -ComObject WScript.Shell

# Backend shortcut
$backendShortcut = $wshell.CreateShortcut("$desktop\CodeMorningstar-Backend.lnk")
$backendShortcut.TargetPath = "powershell.exe"
$backendShortcut.Arguments = "-NoExit -Command \"Set-Location 'C:\Users\<USER>\Code-Morningstar'; ./start_backend.ps1\""
$backendShortcut.WorkingDirectory = "C:\Users\<USER>\Code-Morningstar"
$backendShortcut.IconLocation = "$desktop\powershell.ico"
$backendShortcut.Save()

# Frontend shortcut (local web server)
$frontendShortcut = $wshell.CreateShortcut("$desktop\CodeMorningstar-Frontend.lnk")
$frontendShortcut.TargetPath = "powershell.exe"
$frontendShortcut.Arguments = "-NoExit -Command \"Set-Location 'C:\Users\<USER>\Code-Morningstar'; python -m http.server 8080; Start-Process http://localhost:8080/frontend/chat-ui.html\""
$frontendShortcut.WorkingDirectory = "C:\Users\<USER>\Code-Morningstar"
$frontendShortcut.IconLocation = "$desktop\powershell.ico"
$frontendShortcut.Save()

# Full App shortcut
$fullAppShortcut = $wshell.CreateShortcut("$desktop\CodeMorningstar-App.lnk")
$fullAppShortcut.TargetPath = "powershell.exe"
$fullAppShortcut.Arguments = "-NoExit -Command \"Set-Location 'C:\Users\<USER>\Code-Morningstar'; ./start_app.bat; Start-Process http://localhost:8080/frontend/chat-ui.html\""
$fullAppShortcut.WorkingDirectory = "C:\Users\<USER>\Code-Morningstar"
$fullAppShortcut.IconLocation = "$desktop\powershell.ico"
$fullAppShortcut.Save()

Write-Host "Shortcuts created on your Desktop."
Get-ChildItem "$desktop\CodeMorningstar-*.lnk" | ForEach-Object { $_.IsReadOnly = $true }
Write-Host "Shortcuts set to read-only to help prevent accidental removal."
