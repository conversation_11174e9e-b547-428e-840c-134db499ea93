#!/usr/bin/env node
// ─────────────────────────────────────────────────────────────────────────────
// Advanced ChatGPT‑Style Desktop App for Jan (Code‑Morningstar)
// Professional AI chat interface with full parameter controls and multi-model support
// - All LLM parameter sliders (temp, top_p, top_k, max_tokens, penalties, etc.)
// - Unlimited system prompt editor with presets and categories
// - Multi-model loading and comparison mode
// - Professional desktop UI with collapsible panels
// ─────────────────────────────────────────────────────────────────────────────

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Enhanced Electron package.json
const PACKAGE_JSON = `{
  "name": "jan-advanced-desktop",
  "version": "1.0.0",
  "description": "Advanced ChatGPT-style desktop app for Jan.ai with full parameter controls",
  "main": "main.js",
  "scripts": {
    "start": "electron .",
    "dev": "electron . --dev",
    "build": "electron-builder",
    "build-win": "electron-builder --win",
    "build-mac": "electron-builder --mac",
    "build-linux": "electron-builder --linux"
  },
  "dependencies": {
    "electron": "^27.0.0",
    "node-fetch": "^3.3.2",
    "electron-store": "^8.1.0"
  },
  "devDependencies": {
    "electron-builder": "^24.6.4"
  },
  "build": {
    "appId": "com.codemorningstar.jan-advanced-desktop",
    "productName": "Jan Advanced Desktop",
    "directories": {
      "output": "dist"
    },
    "files": [
      "main.js",
      "preload.js",
      "renderer.html",
      "assets/**/*",
      "package.json"
    ],
    "win": {
      "target": "nsis",
      "icon": "assets/icon.ico"
    },
    "mac": {
      "target": "dmg",
      "icon": "assets/icon.icns"
    },
    "linux": {
      "target": "AppImage",
      "icon": "assets/icon.png"
    }
  },
  "keywords": ["jan", "chatgpt", "desktop", "ai", "electron", "advanced"],
  "author": "Code-Morningstar",
  "license": "MIT"
}`;

// Enhanced main process with multi-model support
const MAIN_JS = `const { app, BrowserWindow, ipcMain, Menu, Tray, dialog } = require('electron');
const path = require('path');
const fetch = require('node-fetch');
const Store = require('electron-store');

let mainWindow;
let tray;
const store = new Store();

// Jan.ai configuration
const JAN_BASE_URL = process.env.JAN_BASE_URL || 'http://127.0.0.1:1337/v1';
const JAN_API_KEY = process.env.JAN_API_KEY || '';

// Default system prompts
const DEFAULT_SYSTEM_PROMPTS = {
  'Coding Assistant': {
    'General Coding': 'You are an expert software developer. Provide clean, efficient, and well-documented code solutions. Explain your reasoning and suggest best practices.',
    'Code Review': 'You are a senior code reviewer. Analyze the provided code for bugs, performance issues, security vulnerabilities, and adherence to best practices. Provide constructive feedback.',
    'Debugging Helper': 'You are a debugging expert. Help identify and fix bugs in code. Provide step-by-step debugging strategies and explain the root cause of issues.',
    'Architecture Advisor': 'You are a software architect. Help design scalable, maintainable software systems. Consider performance, security, and future extensibility.'
  },
  'Creative Writing': {
    'Story Writer': 'You are a creative storyteller. Write engaging, imaginative stories with rich characters and compelling plots.',
    'Technical Writer': 'You are a technical writing expert. Create clear, concise documentation that is easy to understand and follow.',
    'Content Creator': 'You are a content creation specialist. Generate engaging, informative content tailored to the target audience.',
    'Editor': 'You are a professional editor. Improve writing clarity, flow, grammar, and style while preserving the author\\'s voice.'
  },
  'Analysis & Research': {
    'Data Analyst': 'You are a data analysis expert. Interpret data, identify patterns, and provide actionable insights with clear explanations.',
    'Research Assistant': 'You are a research specialist. Provide thorough, well-sourced information and help synthesize complex topics.',
    'Business Analyst': 'You are a business analysis expert. Evaluate business problems and provide strategic recommendations.',
    'Academic Researcher': 'You are an academic researcher. Provide scholarly analysis with proper methodology and evidence-based conclusions.'
  },
  'General Purpose': {
    'Helpful Assistant': 'You are a helpful, harmless, and honest AI assistant. Provide accurate information and assist with a wide variety of tasks.',
    'Conversational': 'You are a friendly conversational partner. Engage in natural, helpful dialogue while being informative and supportive.',
    'Problem Solver': 'You are a problem-solving expert. Break down complex problems into manageable steps and provide practical solutions.',
    'Tutor': 'You are an expert tutor. Explain concepts clearly, provide examples, and adapt your teaching style to help the user learn effectively.'
  }
};

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1000,
    minHeight: 700,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    titleBarStyle: 'hiddenInset',
    show: false,
    icon: path.join(__dirname, 'assets', 'icon.png'),
    backgroundColor: '#0b0f14'
  });

  mainWindow.loadFile('renderer.html');
  
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // Initialize default system prompts if not exists
    if (!store.get('systemPrompts')) {
      store.set('systemPrompts', DEFAULT_SYSTEM_PROMPTS);
    }
  });

  // Create system tray
  createTray();
  
  // Create menu
  createMenu();
}

function createTray() {
  try {
    tray = new Tray(path.join(__dirname, 'assets', 'icon.png'));
    const contextMenu = Menu.buildFromTemplate([
      { label: 'Show', click: () => mainWindow.show() },
      { label: 'Hide', click: () => mainWindow.hide() },
      { type: 'separator' },
      { label: 'Quit', click: () => app.quit() }
    ]);
    tray.setContextMenu(contextMenu);
    tray.setToolTip('Jan Advanced Desktop');
  } catch (error) {
    console.log('Tray creation failed:', error);
  }
}

function createMenu() {
  const template = [
    {
      label: 'File',
      submenu: [
        { label: 'New Chat', accelerator: 'CmdOrCtrl+N', click: () => mainWindow.webContents.send('new-chat') },
        { label: 'Save Chat', accelerator: 'CmdOrCtrl+S', click: () => mainWindow.webContents.send('save-chat') },
        { type: 'separator' },
        { label: 'Import Prompts', click: () => importPrompts() },
        { label: 'Export Prompts', click: () => exportPrompts() },
        { type: 'separator' },
        { role: 'quit' }
      ]
    },
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' },
        { role: 'selectall' }
      ]
    },
    {
      label: 'View',
      submenu: [
        { label: 'Toggle Sidebar', accelerator: 'CmdOrCtrl+B', click: () => mainWindow.webContents.send('toggle-sidebar') },
        { label: 'Toggle Developer Tools', accelerator: 'F12', click: () => mainWindow.webContents.toggleDevTools() },
        { type: 'separator' },
        { role: 'reload' },
        { role: 'forceReload' },
        { type: 'separator' },
        { role: 'resetzoom' },
        { role: 'zoomin' },
        { role: 'zoomout' }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

async function importPrompts() {
  const result = await dialog.showOpenDialog(mainWindow, {
    properties: ['openFile'],
    filters: [{ name: 'JSON Files', extensions: ['json'] }]
  });
  
  if (!result.canceled) {
    try {
      const data = JSON.parse(fs.readFileSync(result.filePaths[0], 'utf8'));
      store.set('systemPrompts', { ...store.get('systemPrompts'), ...data });
      mainWindow.webContents.send('prompts-updated');
    } catch (error) {
      console.error('Import failed:', error);
    }
  }
}

async function exportPrompts() {
  const result = await dialog.showSaveDialog(mainWindow, {
    filters: [{ name: 'JSON Files', extensions: ['json'] }],
    defaultPath: 'system-prompts.json'
  });
  
  if (!result.canceled) {
    try {
      fs.writeFileSync(result.filePath, JSON.stringify(store.get('systemPrompts'), null, 2));
    } catch (error) {
      console.error('Export failed:', error);
    }
  }
}

// IPC handlers for enhanced functionality
ipcMain.handle('get-models', async () => {
  try {
    const response = await fetch(\`\${JAN_BASE_URL}/models\`, {
      headers: {
        'Authorization': JAN_API_KEY ? \`Bearer \${JAN_API_KEY}\` : undefined,
        'Content-Type': 'application/json'
      }
    });
    const data = await response.json();
    return { success: true, data };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('chat-completion', async (event, payload) => {
  try {
    const response = await fetch(\`\${JAN_BASE_URL}/chat/completions\`, {
      method: 'POST',
      headers: {
        'Authorization': JAN_API_KEY ? \`Bearer \${JAN_API_KEY}\` : undefined,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    });

    if (payload.stream) {
      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\\n');

        for (const line of lines) {
          if (line.startsWith('data:')) {
            const data = line.replace(/^data:\\s*/, '').trim();
            if (data === '[DONE]') {
              event.sender.send('chat-stream-end');
              return;
            }
            try {
              const json = JSON.parse(data);
              event.sender.send('chat-stream-data', json);
            } catch (e) {
              // Ignore parsing errors
            }
          }
        }
      }
    } else {
      const data = await response.json();
      return { success: true, data };
    }
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// System prompt management
ipcMain.handle('get-system-prompts', () => {
  return store.get('systemPrompts', DEFAULT_SYSTEM_PROMPTS);
});

ipcMain.handle('save-system-prompt', (event, category, name, prompt) => {
  const prompts = store.get('systemPrompts', {});
  if (!prompts[category]) prompts[category] = {};
  prompts[category][name] = prompt;
  store.set('systemPrompts', prompts);
  return true;
});

ipcMain.handle('delete-system-prompt', (event, category, name) => {
  const prompts = store.get('systemPrompts', {});
  if (prompts[category] && prompts[category][name]) {
    delete prompts[category][name];
    if (Object.keys(prompts[category]).length === 0) {
      delete prompts[category];
    }
    store.set('systemPrompts', prompts);
  }
  return true;
});

// Settings management
ipcMain.handle('get-settings', (event, key) => {
  return store.get(key);
});

ipcMain.handle('save-settings', (event, key, value) => {
  store.set(key, value);
  return true;
});

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});`;

// Enhanced preload script
const PRELOAD_JS = `const { contextBridge, ipcRenderer } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  // Model management
  getModels: () => ipcRenderer.invoke('get-models'),
  chatCompletion: (payload) => ipcRenderer.invoke('chat-completion', payload),

  // Streaming
  onStreamData: (callback) => ipcRenderer.on('chat-stream-data', callback),
  onStreamEnd: (callback) => ipcRenderer.on('chat-stream-end', callback),
  removeStreamListeners: () => {
    ipcRenderer.removeAllListeners('chat-stream-data');
    ipcRenderer.removeAllListeners('chat-stream-end');
  },

  // System prompts
  getSystemPrompts: () => ipcRenderer.invoke('get-system-prompts'),
  saveSystemPrompt: (category, name, prompt) => ipcRenderer.invoke('save-system-prompt', category, name, prompt),
  deleteSystemPrompt: (category, name) => ipcRenderer.invoke('delete-system-prompt', category, name),

  // Settings
  getSettings: (key) => ipcRenderer.invoke('get-settings', key),
  saveSettings: (key, value) => ipcRenderer.invoke('save-settings', key, value),

  // Menu events
  onNewChat: (callback) => ipcRenderer.on('new-chat', callback),
  onSaveChat: (callback) => ipcRenderer.on('save-chat', callback),
  onToggleSidebar: (callback) => ipcRenderer.on('toggle-sidebar', callback),
  onPromptsUpdated: (callback) => ipcRenderer.on('prompts-updated', callback)
});
`;

// Advanced renderer HTML with full parameter controls
const RENDERER_HTML = `<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Jan Advanced Desktop</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
  <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github-dark.min.css"/>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"/>
  <style>
    :root { color-scheme: dark; }
    body {
      background: #0b0f14;
      margin: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      overflow: hidden;
    }
    .msg { white-space: pre-wrap; }
    .bubble { border-radius: 1rem; }
    .scrollbars::-webkit-scrollbar { width: 8px; }
    .scrollbars::-webkit-scrollbar-thumb { background: #374151; border-radius: 8px; }
    .scrollbars::-webkit-scrollbar-track { background: #1f2937; }
    .prose code { white-space: pre-wrap; }
    .codeblock { background: #0b1220; border: 1px solid #1f2a37; border-radius: 12px; padding: 12px; }
    .sidebar-collapsed { width: 60px; }
    .sidebar-expanded { width: 320px; }
    .parameter-slider { appearance: none; background: #374151; height: 4px; border-radius: 2px; }
    .parameter-slider::-webkit-slider-thumb {
      appearance: none; width: 16px; height: 16px; border-radius: 50%;
      background: #3b82f6; cursor: pointer;
    }
    .model-tab { transition: all 0.2s ease; }
    .model-tab.active { background: #1e40af; border-color: #3b82f6; }
    .prompt-editor { resize: vertical; min-height: 100px; max-height: 300px; }
  </style>
</head>
<body class="h-screen text-gray-100">
  <div id="root" class="h-full"></div>

  <script type="module">
    const { useState, useEffect, useRef, useCallback } = React;

    // Default parameter values
    const DEFAULT_PARAMS = {
      temperature: 0.7,
      top_p: 0.9,
      top_k: 40,
      max_tokens: 2048,
      frequency_penalty: 0.0,
      presence_penalty: 0.0,
      repetition_penalty: 1.0,
      min_p: 0.05
    };

    function ParameterSlider({ label, value, onChange, min = 0, max = 1, step = 0.01, description }) {
      return (
        React.createElement('div', { className: 'mb-4' },
          React.createElement('div', { className: 'flex justify-between items-center mb-2' },
            React.createElement('label', { className: 'text-sm font-medium text-gray-300' }, label),
            React.createElement('span', { className: 'text-xs text-blue-400 font-mono' }, value)
          ),
          React.createElement('input', {
            type: 'range',
            min, max, step, value,
            onChange: e => onChange(parseFloat(e.target.value)),
            className: 'parameter-slider w-full'
          }),
          description && React.createElement('p', { className: 'text-xs text-gray-500 mt-1' }, description)
        )
      );
    }

    function SystemPromptEditor({ prompts, selectedCategory, selectedPrompt, onPromptChange, onSave, onDelete }) {
      const [customPrompt, setCustomPrompt] = useState('');
      const [newCategory, setNewCategory] = useState('');
      const [newPromptName, setNewPromptName] = useState('');
      const [showCustomEditor, setShowCustomEditor] = useState(false);

      useEffect(() => {
        if (selectedCategory && selectedPrompt && prompts[selectedCategory]) {
          setCustomPrompt(prompts[selectedCategory][selectedPrompt] || '');
        }
      }, [selectedCategory, selectedPrompt, prompts]);

      const handleSaveCustom = () => {
        if (newCategory && newPromptName && customPrompt) {
          onSave(newCategory, newPromptName, customPrompt);
          setNewCategory('');
          setNewPromptName('');
          setCustomPrompt('');
          setShowCustomEditor(false);
        }
      };

      return (
        React.createElement('div', { className: 'space-y-4' },
          React.createElement('div', { className: 'flex justify-between items-center' },
            React.createElement('h3', { className: 'text-lg font-semibold text-gray-200' }, 'System Prompts'),
            React.createElement('button', {
              onClick: () => setShowCustomEditor(!showCustomEditor),
              className: 'px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-sm'
            }, showCustomEditor ? 'Cancel' : 'Add Custom')
          ),

          // Category selector
          React.createElement('select', {
            value: selectedCategory,
            onChange: e => onPromptChange(e.target.value, ''),
            className: 'w-full bg-gray-800 border border-gray-600 rounded px-3 py-2'
          },
            React.createElement('option', { value: '' }, 'Select Category...'),
            Object.keys(prompts).map(cat =>
              React.createElement('option', { key: cat, value: cat }, cat)
            )
          ),

          // Prompt selector
          selectedCategory && React.createElement('select', {
            value: selectedPrompt,
            onChange: e => onPromptChange(selectedCategory, e.target.value),
            className: 'w-full bg-gray-800 border border-gray-600 rounded px-3 py-2'
          },
            React.createElement('option', { value: '' }, 'Select Prompt...'),
            Object.keys(prompts[selectedCategory] || {}).map(prompt =>
              React.createElement('option', { key: prompt, value: prompt }, prompt)
            )
          ),

          // Prompt preview/editor
          selectedCategory && selectedPrompt && React.createElement('div', { className: 'space-y-2' },
            React.createElement('textarea', {
              value: customPrompt,
              onChange: e => setCustomPrompt(e.target.value),
              className: 'w-full bg-gray-800 border border-gray-600 rounded px-3 py-2 prompt-editor',
              placeholder: 'System prompt content...'
            }),
            React.createElement('div', { className: 'flex gap-2' },
              React.createElement('button', {
                onClick: () => onSave(selectedCategory, selectedPrompt, customPrompt),
                className: 'px-3 py-1 bg-green-600 hover:bg-green-700 rounded text-sm'
              }, 'Update'),
              React.createElement('button', {
                onClick: () => onDelete(selectedCategory, selectedPrompt),
                className: 'px-3 py-1 bg-red-600 hover:bg-red-700 rounded text-sm'
              }, 'Delete')
            )
          ),

          // Custom prompt creator
          showCustomEditor && React.createElement('div', { className: 'space-y-2 border-t border-gray-700 pt-4' },
            React.createElement('input', {
              value: newCategory,
              onChange: e => setNewCategory(e.target.value),
              placeholder: 'Category name...',
              className: 'w-full bg-gray-800 border border-gray-600 rounded px-3 py-2'
            }),
            React.createElement('input', {
              value: newPromptName,
              onChange: e => setNewPromptName(e.target.value),
              placeholder: 'Prompt name...',
              className: 'w-full bg-gray-800 border border-gray-600 rounded px-3 py-2'
            }),
            React.createElement('textarea', {
              value: customPrompt,
              onChange: e => setCustomPrompt(e.target.value),
              placeholder: 'Enter your custom system prompt (no character limit)...',
              className: 'w-full bg-gray-800 border border-gray-600 rounded px-3 py-2 prompt-editor',
              rows: 6
            }),
            React.createElement('button', {
              onClick: handleSaveCustom,
              disabled: !newCategory || !newPromptName || !customPrompt,
              className: 'w-full px-3 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed rounded'
            }, 'Save Custom Prompt')
          )
        )
      );
    }

    function ModelTabs({ models, activeModels, onModelToggle, onModelSelect, selectedModel }) {
      return (
        React.createElement('div', { className: 'space-y-4' },
          React.createElement('h3', { className: 'text-lg font-semibold text-gray-200' }, 'Models'),

          // Available models
          React.createElement('div', { className: 'space-y-2' },
            React.createElement('h4', { className: 'text-sm font-medium text-gray-400' }, 'Available Models'),
            React.createElement('div', { className: 'max-h-40 overflow-y-auto scrollbars space-y-1' },
              models.map(model =>
                React.createElement('div', {
                  key: model,
                  className: 'flex items-center justify-between p-2 bg-gray-800 rounded hover:bg-gray-700'
                },
                  React.createElement('span', { className: 'text-sm truncate flex-1' }, model),
                  React.createElement('button', {
                    onClick: () => onModelToggle(model),
                    className: \\\`px-2 py-1 rounded text-xs \\\${
                      activeModels.includes(model)
                        ? 'bg-green-600 hover:bg-green-700'
                        : 'bg-blue-600 hover:bg-blue-700'
                    }\\\`
                  }, activeModels.includes(model) ? 'Remove' : 'Load')
                )
              )
            )
          ),

          // Active model tabs
          activeModels.length > 0 && React.createElement('div', { className: 'space-y-2' },
            React.createElement('h4', { className: 'text-sm font-medium text-gray-400' }, 'Active Models'),
            React.createElement('div', { className: 'space-y-1' },
              activeModels.map(model =>
                React.createElement('button', {
                  key: model,
                  onClick: () => onModelSelect(model),
                  className: \\\`w-full p-2 rounded text-left text-sm model-tab border \\\${
                    selectedModel === model
                      ? 'active text-white'
                      : 'bg-gray-800 border-gray-600 hover:bg-gray-700 text-gray-300'
                  }\\\`
                },
                  React.createElement('div', { className: 'truncate' }, model),
                  selectedModel === model && React.createElement('div', { className: 'text-xs text-blue-300 mt-1' }, 'Active')
                )
              )
            )
          )
        )
      );
    }

    function Sidebar({
      isCollapsed,
      onToggle,
      models,
      activeModels,
      selectedModel,
      onModelToggle,
      onModelSelect,
      parameters,
      onParameterChange,
      prompts,
      selectedCategory,
      selectedPrompt,
      onPromptChange,
      onPromptSave,
      onPromptDelete
    }) {
      const [activeTab, setActiveTab] = useState('models');

      return (
        React.createElement('div', {
          className: \\\`bg-gray-900 border-r border-gray-700 transition-all duration-300 flex flex-col \\\${
            isCollapsed ? 'sidebar-collapsed' : 'sidebar-expanded'
          }\\\`
        },
          // Sidebar header
          React.createElement('div', { className: 'p-4 border-b border-gray-700 flex items-center justify-between' },
            !isCollapsed && React.createElement('h2', { className: 'text-lg font-semibold' }, 'Controls'),
            React.createElement('button', {
              onClick: onToggle,
              className: 'p-2 hover:bg-gray-800 rounded'
            }, React.createElement('i', { className: \\\`fas \\\${isCollapsed ? 'fa-chevron-right' : 'fa-chevron-left'}\\\` }))
          ),

          !isCollapsed && React.createElement('div', { className: 'flex-1 overflow-hidden flex flex-col' },
            // Tab navigation
            React.createElement('div', { className: 'flex border-b border-gray-700' },
              ['models', 'params', 'prompts'].map(tab =>
                React.createElement('button', {
                  key: tab,
                  onClick: () => setActiveTab(tab),
                  className: \\\`flex-1 p-3 text-sm font-medium capitalize \\\${
                    activeTab === tab
                      ? 'bg-gray-800 text-blue-400 border-b-2 border-blue-400'
                      : 'text-gray-400 hover:text-gray-200 hover:bg-gray-800'
                  }\\\`
                }, tab)
              )
            ),

            // Tab content
            React.createElement('div', { className: 'flex-1 overflow-y-auto scrollbars p-4' },
              activeTab === 'models' && React.createElement(ModelTabs, {
                models,
                activeModels,
                onModelToggle,
                onModelSelect,
                selectedModel
              }),

              activeTab === 'params' && React.createElement('div', { className: 'space-y-4' },
                React.createElement('h3', { className: 'text-lg font-semibold text-gray-200' }, 'Parameters'),
                React.createElement(ParameterSlider, {
                  label: 'Temperature',
                  value: parameters.temperature,
                  onChange: v => onParameterChange('temperature', v),
                  min: 0, max: 2, step: 0.01,
                  description: 'Controls randomness. Higher = more creative, lower = more focused.'
                }),
                React.createElement(ParameterSlider, {
                  label: 'Top-p',
                  value: parameters.top_p,
                  onChange: v => onParameterChange('top_p', v),
                  min: 0, max: 1, step: 0.01,
                  description: 'Nucleus sampling. Consider tokens with cumulative probability up to p.'
                }),
                React.createElement(ParameterSlider, {
                  label: 'Top-k',
                  value: parameters.top_k,
                  onChange: v => onParameterChange('top_k', v),
                  min: 1, max: 100, step: 1,
                  description: 'Consider only the top k most likely tokens.'
                }),
                React.createElement(ParameterSlider, {
                  label: 'Max Tokens',
                  value: parameters.max_tokens,
                  onChange: v => onParameterChange('max_tokens', v),
                  min: 1, max: 8192, step: 1,
                  description: 'Maximum number of tokens to generate.'
                }),
                React.createElement(ParameterSlider, {
                  label: 'Frequency Penalty',
                  value: parameters.frequency_penalty,
                  onChange: v => onParameterChange('frequency_penalty', v),
                  min: -2, max: 2, step: 0.01,
                  description: 'Penalize tokens based on their frequency in the text.'
                }),
                React.createElement(ParameterSlider, {
                  label: 'Presence Penalty',
                  value: parameters.presence_penalty,
                  onChange: v => onParameterChange('presence_penalty', v),
                  min: -2, max: 2, step: 0.01,
                  description: 'Penalize tokens based on whether they appear in the text.'
                }),
                React.createElement(ParameterSlider, {
                  label: 'Repetition Penalty',
                  value: parameters.repetition_penalty,
                  onChange: v => onParameterChange('repetition_penalty', v),
                  min: 0.5, max: 2, step: 0.01,
                  description: 'Penalize repetitive tokens. 1.0 = no penalty.'
                }),
                React.createElement(ParameterSlider, {
                  label: 'Min-p',
                  value: parameters.min_p,
                  onChange: v => onParameterChange('min_p', v),
                  min: 0, max: 1, step: 0.01,
                  description: 'Minimum probability threshold for token selection.'
                })
              ),

              activeTab === 'prompts' && React.createElement(SystemPromptEditor, {
                prompts,
                selectedCategory,
                selectedPrompt,
                onPromptChange,
                onSave: onPromptSave,
                onDelete: onPromptDelete
              })
            )
          )
        )
      );
    }

    function Message({ role, content, model }) {
      const containerRef = useRef(null);

      useEffect(() => {
        if (containerRef.current) {
          containerRef.current.querySelectorAll('pre code').forEach(block => hljs.highlightElement(block));
        }
      }, [content]);

      const isUser = role === 'user';

      return (
        React.createElement('div', { className: \\\`flex \\\${isUser ? 'justify-end' : 'justify-start'} w-full mb-4\\\` },
          React.createElement('div', {
            className: \\\`bubble max-w-4xl \\\${
              isUser
                ? 'bg-blue-600/20 border-blue-500/30'
                : 'bg-gray-800/60 border-gray-700'
            } border p-4\\\`
          },
            !isUser && model && React.createElement('div', { className: 'text-xs text-gray-400 mb-2' }, \\\`Model: \\\${model}\\\`),
            React.createElement('div', {
              ref: containerRef,
              className: 'prose prose-invert prose-pre:my-2 prose-code:px-1 prose-code:py-0.5 max-w-none'
            }, renderMarkdown(content))
          )
        )
      );
    }

    function renderMarkdown(text) {
      const parts = [];
      const regex = /\\\`\\\`\\\`(\\\\w+)?\\\\n([\\\\s\\\\S]*?)\\\`\\\`\\\`/g;
      let lastIndex = 0, m;

      while ((m = regex.exec(text)) !== null) {
        if (m.index > lastIndex) {
          parts.push(React.createElement('div', { key: \\\`t-\\\${lastIndex}\\\` }, text.slice(lastIndex, m.index)));
        }

        const lang = m[1] || 'plaintext';
        const code = m[2];

        parts.push(
          React.createElement('div', { className: 'codeblock my-3', key: \\\`c-\\\${m.index}\\\` },
            React.createElement('div', { className: 'flex justify-between items-center mb-2 text-xs text-gray-400' },
              React.createElement('span', null, lang),
              React.createElement('button', {
                className: 'px-2 py-1 border border-gray-600 rounded hover:bg-gray-700 transition-colors',
                onClick: () => navigator.clipboard.writeText(code)
              }, React.createElement('i', { className: 'fas fa-copy mr-1' }), 'Copy')
            ),
            React.createElement('pre', { className: 'overflow-x-auto' },
              React.createElement('code', { className: \\\`language-\\\${lang}\\\` }, code)
            )
          )
        );
        lastIndex = regex.lastIndex;
      }

      if (lastIndex < text.length) {
        parts.push(React.createElement('div', { key: 't-end' }, text.slice(lastIndex)));
      }

      return parts.length > 0 ? parts : text;
    }

    function ChatArea({ messages, onSend, onStop, streaming, input, setInput, selectedModel, currentSystemPrompt }) {
      const scrollRef = useRef(null);
      const textareaRef = useRef(null);

      useEffect(() => {
        if (scrollRef.current) {
          scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
        }
      }, [messages, streaming]);

      const handleKeyDown = (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          if (!streaming && input.trim()) {
            onSend();
          }
        }
      };

      const adjustTextareaHeight = () => {
        if (textareaRef.current) {
          textareaRef.current.style.height = 'auto';
          textareaRef.current.style.height = Math.min(textareaRef.current.scrollHeight, 200) + 'px';
        }
      };

      useEffect(() => {
        adjustTextareaHeight();
      }, [input]);

      return (
        React.createElement('div', { className: 'flex-1 flex flex-col h-full' },
          // Header
          React.createElement('div', { className: 'p-4 border-b border-gray-700 bg-gray-900/50' },
            React.createElement('div', { className: 'flex items-center justify-between' },
              React.createElement('div', null,
                React.createElement('h1', { className: 'text-xl font-semibold' }, 'Jan Advanced Desktop'),
                selectedModel && React.createElement('p', { className: 'text-sm text-gray-400' }, \\\`Active Model: \\\${selectedModel}\\\`)
              ),
              React.createElement('div', { className: 'flex items-center gap-2' },
                React.createElement('button', {
                  onClick: () => window.electronAPI && window.electronAPI.onNewChat && window.electronAPI.onNewChat(() => {}),
                  className: 'px-3 py-1 bg-gray-700 hover:bg-gray-600 rounded text-sm'
                }, React.createElement('i', { className: 'fas fa-plus mr-1' }), 'New Chat'),
                currentSystemPrompt && React.createElement('span', {
                  className: 'text-xs bg-blue-600/20 text-blue-300 px-2 py-1 rounded',
                  title: currentSystemPrompt
                }, 'System Prompt Active')
              )
            )
          ),

          // Messages
          React.createElement('div', {
            ref: scrollRef,
            className: 'flex-1 overflow-y-auto scrollbars p-4 space-y-2'
          },
            messages.length === 0 && React.createElement('div', { className: 'text-center text-gray-500 mt-20' },
              React.createElement('i', { className: 'fas fa-robot text-4xl mb-4' }),
              React.createElement('p', null, 'Start a conversation with Jan AI'),
              React.createElement('p', { className: 'text-sm mt-2' }, 'Use the sidebar to configure models, parameters, and system prompts')
            ),
            messages.map((msg, i) =>
              React.createElement(Message, {
                key: i,
                role: msg.role,
                content: msg.content,
                model: msg.model
              })
            )
          ),

          // Input area
          React.createElement('div', { className: 'p-4 border-t border-gray-700 bg-gray-900/50' },
            React.createElement('div', { className: 'max-w-4xl mx-auto' },
              React.createElement('div', { className: 'flex items-end gap-3' },
                React.createElement('div', { className: 'flex-1' },
                  React.createElement('textarea', {
                    ref: textareaRef,
                    value: input,
                    onChange: e => setInput(e.target.value),
                    onKeyDown: handleKeyDown,
                    placeholder: selectedModel ? 'Type your message...' : 'Please select a model first',
                    disabled: !selectedModel,
                    className: 'w-full resize-none bg-gray-800 border border-gray-600 rounded-xl p-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed',
                    style: { minHeight: '44px', maxHeight: '200px' }
                  })
                ),
                streaming ? (
                  React.createElement('button', {
                    onClick: onStop,
                    className: 'px-4 py-2 bg-red-600 hover:bg-red-700 rounded-lg font-medium transition-colors'
                  }, React.createElement('i', { className: 'fas fa-stop mr-1' }), 'Stop')
                ) : (
                  React.createElement('button', {
                    onClick: onSend,
                    disabled: !input.trim() || !selectedModel,
                    className: 'px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed rounded-lg font-medium transition-colors'
                  }, React.createElement('i', { className: 'fas fa-paper-plane mr-1' }), 'Send')
                )
              )
            )
          )
        )
      );
    }

    function App() {
      // State management
      const [models, setModels] = useState([]);
      const [activeModels, setActiveModels] = useState([]);
      const [selectedModel, setSelectedModel] = useState('');
      const [parameters, setParameters] = useState(DEFAULT_PARAMS);
      const [messages, setMessages] = useState([]);
      const [input, setInput] = useState('');
      const [streaming, setStreaming] = useState(false);
      const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
      const [systemPrompts, setSystemPrompts] = useState({});
      const [selectedCategory, setSelectedCategory] = useState('');
      const [selectedPrompt, setSelectedPrompt] = useState('');
      const abortRef = useRef(null);

      // Load initial data
      useEffect(() => {
        loadModels();
        loadSystemPrompts();
        loadSettings();
      }, []);

      const loadModels = async () => {
        try {
          const result = await window.electronAPI.getModels();
          if (result.success) {
            const modelList = (result.data?.data || result.data?.models || []).map(m => m.id || m);
            setModels(modelList);
          }
        } catch (error) {
          console.error('Failed to load models:', error);
        }
      };

      const loadSystemPrompts = async () => {
        try {
          const prompts = await window.electronAPI.getSystemPrompts();
          setSystemPrompts(prompts);
        } catch (error) {
          console.error('Failed to load system prompts:', error);
        }
      };

      const loadSettings = async () => {
        try {
          const savedParams = await window.electronAPI.getSettings('parameters');
          if (savedParams) setParameters({ ...DEFAULT_PARAMS, ...savedParams });

          const savedActiveModels = await window.electronAPI.getSettings('activeModels');
          if (savedActiveModels) setActiveModels(savedActiveModels);

          const savedSelectedModel = await window.electronAPI.getSettings('selectedModel');
          if (savedSelectedModel) setSelectedModel(savedSelectedModel);

          const savedSidebarState = await window.electronAPI.getSettings('sidebarCollapsed');
          if (savedSidebarState !== undefined) setSidebarCollapsed(savedSidebarState);
        } catch (error) {
          console.error('Failed to load settings:', error);
        }
      };

      // Event handlers
      const handleModelToggle = (model) => {
        const newActiveModels = activeModels.includes(model)
          ? activeModels.filter(m => m !== model)
          : [...activeModels, model];

        setActiveModels(newActiveModels);
        window.electronAPI.saveSettings('activeModels', newActiveModels);

        if (!newActiveModels.includes(selectedModel)) {
          const newSelected = newActiveModels[0] || '';
          setSelectedModel(newSelected);
          window.electronAPI.saveSettings('selectedModel', newSelected);
        }
      };

      const handleModelSelect = (model) => {
        setSelectedModel(model);
        window.electronAPI.saveSettings('selectedModel', model);
      };

      const handleParameterChange = (param, value) => {
        const newParams = { ...parameters, [param]: value };
        setParameters(newParams);
        window.electronAPI.saveSettings('parameters', newParams);
      };

      const handlePromptChange = (category, prompt) => {
        setSelectedCategory(category);
        setSelectedPrompt(prompt);
      };

      const handlePromptSave = async (category, name, prompt) => {
        try {
          await window.electronAPI.saveSystemPrompt(category, name, prompt);
          await loadSystemPrompts();
        } catch (error) {
          console.error('Failed to save prompt:', error);
        }
      };

      const handlePromptDelete = async (category, name) => {
        try {
          await window.electronAPI.deleteSystemPrompt(category, name);
          await loadSystemPrompts();
          if (selectedCategory === category && selectedPrompt === name) {
            setSelectedCategory('');
            setSelectedPrompt('');
          }
        } catch (error) {
          console.error('Failed to delete prompt:', error);
        }
      };

      const toggleSidebar = () => {
        const newState = !sidebarCollapsed;
        setSidebarCollapsed(newState);
        window.electronAPI.saveSettings('sidebarCollapsed', newState);
      };

      const sendMessage = async () => {
        if (!input.trim() || !selectedModel || streaming) return;

        const userMessage = { role: 'user', content: input.trim() };
        const systemPrompt = selectedCategory && selectedPrompt && systemPrompts[selectedCategory]
          ? systemPrompts[selectedCategory][selectedPrompt]
          : null;

        // Prepare messages with system prompt
        const chatMessages = [];
        if (systemPrompt) {
          chatMessages.push({ role: 'system', content: systemPrompt });
        }
        chatMessages.push(...messages, userMessage);

        const assistantMessage = { role: 'assistant', content: '', model: selectedModel };
        setMessages(prev => [...prev, userMessage, assistantMessage]);
        setInput('');
        setStreaming(true);

        const controller = new AbortController();
        abortRef.current = controller;

        try {
          // Setup streaming listeners
          const handleStreamData = (event, data) => {
            const delta = data?.choices?.[0]?.delta?.content || '';
            if (delta) {
              setMessages(prev => {
                const newMessages = [...prev];
                const lastMessage = newMessages[newMessages.length - 1];
                if (lastMessage.role === 'assistant') {
                  lastMessage.content += delta;
                }
                return newMessages;
              });
            }
          };

          const handleStreamEnd = () => {
            setStreaming(false);
            abortRef.current = null;
            window.electronAPI.removeStreamListeners();
          };

          window.electronAPI.onStreamData(handleStreamData);
          window.electronAPI.onStreamEnd(handleStreamEnd);

          // Send chat completion request
          const payload = {
            model: selectedModel,
            messages: chatMessages.map(m => ({ role: m.role, content: m.content })),
            stream: true,
            ...parameters
          };

          await window.electronAPI.chatCompletion(payload);

        } catch (error) {
          console.error('Chat error:', error);
          setMessages(prev => {
            const newMessages = [...prev];
            const lastMessage = newMessages[newMessages.length - 1];
            if (lastMessage.role === 'assistant') {
              lastMessage.content = \\\`Error: \\\${error.message}\\\`;
            }
            return newMessages;
          });
          setStreaming(false);
          abortRef.current = null;
        }
      };

      const stopGeneration = () => {
        if (abortRef.current) {
          abortRef.current.abort();
          setStreaming(false);
          abortRef.current = null;
          window.electronAPI.removeStreamListeners();
        }
      };

      const clearChat = () => {
        setMessages([]);
      };

      // Get current system prompt for display
      const currentSystemPrompt = selectedCategory && selectedPrompt && systemPrompts[selectedCategory]
        ? systemPrompts[selectedCategory][selectedPrompt]
        : null;

      return (
        React.createElement('div', { className: 'h-screen flex bg-gray-900 text-gray-100' },
          React.createElement(Sidebar, {
            isCollapsed: sidebarCollapsed,
            onToggle: toggleSidebar,
            models,
            activeModels,
            selectedModel,
            onModelToggle: handleModelToggle,
            onModelSelect: handleModelSelect,
            parameters,
            onParameterChange: handleParameterChange,
            prompts: systemPrompts,
            selectedCategory,
            selectedPrompt,
            onPromptChange: handlePromptChange,
            onPromptSave: handlePromptSave,
            onPromptDelete: handlePromptDelete
          }),
          React.createElement(ChatArea, {
            messages,
            onSend: sendMessage,
            onStop: stopGeneration,
            streaming,
            input,
            setInput,
            selectedModel,
            currentSystemPrompt
          })
        )
      );
    }

    // Initialize the app
    ReactDOM.createRoot(document.getElementById('root')).render(React.createElement(App));
  </script>
</body>
</html>`;

// Setup functions
function createDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`✓ Created directory: ${dirPath}`);
  }
}

function writeFile(filePath, content) {
  fs.writeFileSync(filePath, content, 'utf8');
  console.log(`✓ Created file: ${filePath}`);
}

function createAssets() {
  createDirectory('assets');

  // Create a simple icon (you can replace with actual icons)
  const iconSvg = `<svg width="256" height="256" viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg">
    <rect width="256" height="256" fill="#1e40af"/>
    <circle cx="128" cy="128" r="80" fill="#3b82f6"/>
    <text x="128" y="140" text-anchor="middle" fill="white" font-size="60" font-family="Arial">J</text>
  </svg>`;

  writeFile('assets/icon.svg', iconSvg);
  console.log('💡 Note: Replace assets/icon.svg with proper .ico/.icns/.png icons for production builds');
}

// Main setup function
function setup() {
  console.log('🚀 Setting up Advanced Jan Desktop App...\n');

  // Create all files
  writeFile('package.json', PACKAGE_JSON);
  writeFile('main.js', MAIN_JS);
  writeFile('preload.js', PRELOAD_JS);
  writeFile('renderer.html', RENDERER_HTML);

  // Create assets
  createAssets();

  console.log('\n✅ Setup complete! Files created:');
  console.log('   📦 package.json');
  console.log('   🖥️  main.js (Electron main process)');
  console.log('   🔒 preload.js (Secure IPC bridge)');
  console.log('   🌐 renderer.html (Advanced UI)');
  console.log('   📁 assets/ (Icons)');

  console.log('\n🎯 Features included:');
  console.log('   • All LLM parameter sliders (temp, top_p, top_k, max_tokens, penalties)');
  console.log('   • Unlimited system prompt editor with categories');
  console.log('   • Multi-model loading and switching');
  console.log('   • Professional ChatGPT-style interface');
  console.log('   • Code syntax highlighting');
  console.log('   • Collapsible sidebar with tabs');
  console.log('   • Settings persistence');
  console.log('   • Import/export system prompts');

  console.log('\n📋 Next steps:');
  console.log('1. Install dependencies: npm install');
  console.log('2. Set environment variables (optional):');
  console.log('   setx JAN_BASE_URL "http://127.0.0.1:1337/v1"');
  console.log('   setx JAN_API_KEY "<YOUR_API_KEY>"');
  console.log('3. Start the app: npm start');
  console.log('4. Build for distribution: npm run build-win');
}

// Run setup
setup();
