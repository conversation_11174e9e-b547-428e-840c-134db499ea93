#!/usr/bin/env python3
"""
Code Morningstar Debug Test Script
Tests all components and shows debug information.
"""

import sys
import os
from pathlib import Path

def test_imports():
    """Test if all required modules can be imported."""
    print("🔍 Testing imports...")
    
    try:
        import fastapi
        print(f"✅ FastAPI: {fastapi.__version__}")
    except ImportError as e:
        print(f"❌ FastAPI import failed: {e}")
        return False
    
    try:
        import uvicorn
        print(f"✅ Uvicorn: {uvicorn.__version__}")
    except ImportError as e:
        print(f"❌ Uvicorn import failed: {e}")
        return False
        
    try:
        import llama_cpp
        print(f"✅ llama-cpp-python: {llama_cpp.__version__}")
    except ImportError as e:
        print(f"⚠️ llama-cpp-python not installed (will use mock mode): {e}")
    
    return True

def test_file_structure():
    """Test if all required files exist."""
    print("\n📁 Testing file structure...")
    
    required_files = [
        "backend/app/main.py",
        "backend/start.py",
        "frontend/standalone.html",
        "deploy.py",
        "Code-Morningstar"
    ]
    
    all_good = True
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - MISSING")
            all_good = False
    
    return all_good

def test_model_file():
    """Test if model file exists."""
    print("\n🤖 Testing model file...")
    
    model_path = Path("backend/models/codellama-70b-instruct.Q5_K_M.gguf")
    if model_path.exists():
        size_gb = model_path.stat().st_size / (1024**3)
        print(f"✅ Model found: {model_path} ({size_gb:.1f} GB)")
        return True
    else:
        print(f"❌ Model not found: {model_path}")
        print("   Application requires a valid model file to run")
        return False

def test_backend_syntax():
    """Test if backend code has syntax errors."""
    print("\n🐍 Testing backend syntax...")
    
    try:
        # Change to backend directory
        os.chdir("backend")
        
        # Test main.py syntax
        import ast
        with open("app/main.py", "r") as f:
            content = f.read()
        ast.parse(content)
        print("✅ Backend main.py syntax OK")
        
        # Test start.py syntax
        with open("start.py", "r") as f:
            content = f.read()
        ast.parse(content)
        print("✅ Backend start.py syntax OK")
        
        # Go back to root
        os.chdir("..")
        return True
        
    except SyntaxError as e:
        print(f"❌ Syntax error: {e}")
        os.chdir("..")
        return False
    except Exception as e:
        print(f"❌ Error testing syntax: {e}")
        os.chdir("..")
        return False

def test_environment():
    """Test environment setup."""
    print("\n🌍 Testing environment...")
    
    env_file = Path("backend/.env")
    env_example = Path("backend/.env.example")
    
    if env_file.exists():
        print("✅ .env file exists")
    elif env_example.exists():
        print("⚠️ .env missing but .env.example exists")
        print("   Run deploy.py to auto-create .env")
    else:
        print("❌ No .env or .env.example found")
        return False
    
    return True

def main():
    """Run all tests."""
    print("🌟 Code Morningstar Debug Test")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("File Structure", test_file_structure),
        ("Model File", test_model_file),
        ("Backend Syntax", test_backend_syntax),
        ("Environment", test_environment)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS:")
    print("=" * 50)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name:20} {status}")
        if not passed:
            all_passed = False
    
    print("=" * 50)
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print("\nTo start Code Morningstar:")
        print("  python backend/start.py")
        print("  OR")
        print("  python deploy.py")
    else:
        print("⚠️ SOME TESTS FAILED!")
        print("\nCheck the errors above and fix them before starting.")

if __name__ == "__main__":
    main()
