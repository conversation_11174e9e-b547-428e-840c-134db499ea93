# 🌟 Code Morningstar

Enterprise-grade AI Code Generation Platform with local CodeLlama 70B integration.

## Features

- 🤖 **Local LLM Integration**: CodeLlama 70B model support
- 🚀 **FastAPI Backend**: High-performance async API
- 🎨 **Modern Svelte Frontend**: TypeScript + Vite
- 🗄️ **Multi-Database Support**: MongoDB, PostgreSQL, Redis, etc.
- 🐳 **Docker Ready**: Complete containerization
- ☸️ **Kubernetes Deployment**: Production-ready orchestration
- 🏗️ **Infrastructure as Code**: Terraform configurations
- 🔧 **Feature Flags**: Dynamic feature management

## Quick Start

### Prerequisites
- Python 3.8+
- Node.js 18+
- 45GB+ free space for CodeLlama 70B model

### Installation

1. **Configure Environment**
   ```bash
   cd backend
   cp .env.example .env
   # Edit .env with your model path
   ```

2. **Install Dependencies**
   ```bash
   # Backend
   cd backend
   pip install -r requirements.txt

   # Frontend
   cd ../frontend
   npm install
   ```

3. **Start Services**
   ```bash
   # Terminal 1: Backend
   cd backend
   uvicorn app.main:app --reload

   # Terminal 2: Frontend
   cd frontend
   npm run dev
   ```

4. **Access Application**
   - Frontend: http://localhost:5173
   - API Docs: http://localhost:8000/docs

## Configuration

Configure your CodeLlama model path in `backend/.env`:
```env
LLM_MODEL_PATH=path/to/your/codellama-70b-instruct.Q5_K_M.gguf
```

## Docker Deployment

```bash
docker-compose up --build
```

## Architecture

- **Backend**: FastAPI + Python
- **Frontend**: Svelte + TypeScript + Vite
- **Model**: CodeLlama 70B GGUF format
- **Databases**: Multi-database architecture
- **Deployment**: Docker + Kubernetes ready

## API Endpoints

- `GET /` - API information
- `GET /llm/health` - Model status
- `POST /llm/generate` - Code generation
- `GET /docs` - Interactive API documentation

## Development

The project uses modern development practices:
- Type safety with TypeScript
- Code formatting with Black/Prettier
- Testing with pytest/Jest
- CI/CD with GitHub Actions

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
