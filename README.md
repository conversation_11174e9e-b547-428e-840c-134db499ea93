# ChatGPT-Style UI for Jan (Code-Morningstar)

A lightweight, streaming-enabled ChatGPT-style interface for Jan.ai with code syntax highlighting.

## Features

- 🚀 Zero-build React UI (CDN-based)
- 📡 Real-time streaming responses
- 🎨 ChatGPT-like dark theme
- 💻 Code syntax highlighting
- 🔒 Secure proxy (API key never touches browser)
- ⚡ Fast setup and deployment

## Quick Start

1. **Set environment variables:**
   ```bash
   setx JAN_BASE_URL "http://127.0.0.1:1337/v1"
   setx JAN_API_KEY "<YOUR_32+_CHAR_KEY>"
   setx UI_PORT "5175"
   ```

2. **Install and run:**
   ```bash
   npm install
   npm start
   ```

3. **Open in browser:**
   http://127.0.0.1:5175

## Architecture

- **server.js**: Node.js proxy server (handles CORS, API key security)
- **public/index.html**: React UI with streaming support
- **Ports**: UI on 5175, Jan on 1337, Tower on 8000

## Security Notes

- API key never reaches the browser
- Proxy handles all Jan.ai communication
- Keep Jan bound to 127.0.0.1 for security
