["tests/test_db_services.py::test_insert_and_select", "tests/test_llm_service.py::test_llm_service_generate_empty_prompt", "tests/test_llm_service.py::test_llm_service_generate_with_mock", "tests/test_llm_service.py::test_llm_service_generate_with_parameters", "tests/test_llm_service.py::test_llm_service_initialization", "tests/test_llm_service.py::test_llm_service_initialization_nonexistent_model", "tests/test_llm_service.py::test_llm_service_model_loaded_status"]