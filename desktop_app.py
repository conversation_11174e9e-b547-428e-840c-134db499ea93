#!/usr/bin/env python3
"""
Code Morningstar Desktop App
Beautiful desktop interface for your AI coding assistant
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import requests
import json
import threading
import time
from datetime import datetime
import sys
import os

class CodeMorningstarApp:
    def __init__(self, root):
        self.root = root
        self.setup_window()
        self.setup_styles()
        self.create_widgets()
        self.api_base = "http://localhost:8000"
        self.check_server_status()
        
    def setup_window(self):
        """Setup main window properties"""
        self.root.title("🌟 Code Morningstar v2.0 - Desktop AI Assistant")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # Modern dark theme colors
        self.colors = {
            'bg': '#1e1e1e',
            'fg': '#ffffff',
            'accent': '#007acc',
            'success': '#4caf50',
            'warning': '#ff9800',
            'error': '#f44336',
            'card': '#2d2d2d',
            'border': '#404040'
        }
        
        self.root.configure(bg=self.colors['bg'])
        
    def setup_styles(self):
        """Setup ttk styles for modern look"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure styles
        style.configure('Title.TLabel', 
                       background=self.colors['bg'], 
                       foreground=self.colors['accent'],
                       font=('Segoe UI', 16, 'bold'))
        
        style.configure('Status.TLabel',
                       background=self.colors['bg'],
                       foreground=self.colors['success'],
                       font=('Segoe UI', 10))
        
        style.configure('Modern.TButton',
                       background=self.colors['accent'],
                       foreground='white',
                       font=('Segoe UI', 10, 'bold'),
                       padding=(10, 5))
        
        style.configure('Modern.TFrame',
                       background=self.colors['bg'],
                       relief='flat')
        
    def create_widgets(self):
        """Create all UI widgets"""
        # Main container
        main_frame = ttk.Frame(self.root, style='Modern.TFrame')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Header
        self.create_header(main_frame)
        
        # Status bar
        self.create_status_bar(main_frame)
        
        # Main content area
        content_frame = ttk.Frame(main_frame, style='Modern.TFrame')
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # Left panel - Input
        self.create_input_panel(content_frame)
        
        # Right panel - Output
        self.create_output_panel(content_frame)
        
        # Bottom panel - Controls
        self.create_controls_panel(main_frame)
        
    def create_header(self, parent):
        """Create header with title and logo"""
        header_frame = ttk.Frame(parent, style='Modern.TFrame')
        header_frame.pack(fill=tk.X, pady=(0, 10))
        
        title_label = ttk.Label(header_frame, 
                               text="🌟 Code Morningstar v2.0", 
                               style='Title.TLabel')
        title_label.pack(side=tk.LEFT)
        
        subtitle_label = ttk.Label(header_frame,
                                  text="Enterprise AI Code Generation Platform",
                                  background=self.colors['bg'],
                                  foreground=self.colors['fg'],
                                  font=('Segoe UI', 10))
        subtitle_label.pack(side=tk.LEFT, padx=(10, 0))
        
    def create_status_bar(self, parent):
        """Create status bar"""
        status_frame = ttk.Frame(parent, style='Modern.TFrame')
        status_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.status_label = ttk.Label(status_frame,
                                     text="🔄 Checking server status...",
                                     style='Status.TLabel')
        self.status_label.pack(side=tk.LEFT)
        
        self.model_label = ttk.Label(status_frame,
                                    text="Model: Loading...",
                                    background=self.colors['bg'],
                                    foreground=self.colors['fg'],
                                    font=('Segoe UI', 9))
        self.model_label.pack(side=tk.RIGHT)
        
    def create_input_panel(self, parent):
        """Create input panel"""
        # Left side - Input
        input_frame = ttk.Frame(parent, style='Modern.TFrame')
        input_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # Input label
        input_label = ttk.Label(input_frame,
                               text="💬 Your Prompt:",
                               background=self.colors['bg'],
                               foreground=self.colors['fg'],
                               font=('Segoe UI', 12, 'bold'))
        input_label.pack(anchor=tk.W, pady=(0, 5))
        
        # Input text area
        self.input_text = scrolledtext.ScrolledText(
            input_frame,
            height=15,
            font=('Consolas', 11),
            bg=self.colors['card'],
            fg=self.colors['fg'],
            insertbackground=self.colors['fg'],
            selectbackground=self.colors['accent'],
            relief='flat',
            borderwidth=1
        )
        self.input_text.pack(fill=tk.BOTH, expand=True)
        
        # Placeholder text
        placeholder = """Enter your coding prompt here...

Examples:
• Create a Python web scraper with async/await
• Analyze this code for security vulnerabilities
• Design a microservices architecture for e-commerce
• Optimize this algorithm for better performance"""
        
        self.input_text.insert('1.0', placeholder)
        self.input_text.bind('<FocusIn>', self.clear_placeholder)
        
    def create_output_panel(self, parent):
        """Create output panel"""
        # Right side - Output
        output_frame = ttk.Frame(parent, style='Modern.TFrame')
        output_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # Output label
        output_label = ttk.Label(output_frame,
                                text="🤖 AI Response:",
                                background=self.colors['bg'],
                                foreground=self.colors['fg'],
                                font=('Segoe UI', 12, 'bold'))
        output_label.pack(anchor=tk.W, pady=(0, 5))
        
        # Output text area
        self.output_text = scrolledtext.ScrolledText(
            output_frame,
            height=15,
            font=('Consolas', 11),
            bg=self.colors['card'],
            fg=self.colors['fg'],
            insertbackground=self.colors['fg'],
            selectbackground=self.colors['accent'],
            relief='flat',
            borderwidth=1,
            state=tk.DISABLED
        )
        self.output_text.pack(fill=tk.BOTH, expand=True)
        
    def create_controls_panel(self, parent):
        """Create controls panel"""
        controls_frame = ttk.Frame(parent, style='Modern.TFrame')
        controls_frame.pack(fill=tk.X, pady=(10, 0))
        
        # Parameters frame
        params_frame = ttk.Frame(controls_frame, style='Modern.TFrame')
        params_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # Temperature control
        temp_frame = ttk.Frame(params_frame, style='Modern.TFrame')
        temp_frame.pack(side=tk.LEFT, padx=(0, 20))
        
        ttk.Label(temp_frame, text="Temperature:", 
                 background=self.colors['bg'], foreground=self.colors['fg']).pack()
        self.temp_var = tk.DoubleVar(value=0.1)
        temp_scale = ttk.Scale(temp_frame, from_=0.0, to=1.0, 
                              variable=self.temp_var, length=100)
        temp_scale.pack()
        self.temp_label = ttk.Label(temp_frame, text="0.1",
                                   background=self.colors['bg'], foreground=self.colors['fg'])
        self.temp_label.pack()
        temp_scale.configure(command=self.update_temp_label)
        
        # Max tokens control
        tokens_frame = ttk.Frame(params_frame, style='Modern.TFrame')
        tokens_frame.pack(side=tk.LEFT, padx=(0, 20))
        
        ttk.Label(tokens_frame, text="Max Tokens:",
                 background=self.colors['bg'], foreground=self.colors['fg']).pack()
        self.tokens_var = tk.IntVar(value=4096)
        tokens_scale = ttk.Scale(tokens_frame, from_=512, to=8192,
                                variable=self.tokens_var, length=100)
        tokens_scale.pack()
        self.tokens_label = ttk.Label(tokens_frame, text="4096",
                                     background=self.colors['bg'], foreground=self.colors['fg'])
        self.tokens_label.pack()
        tokens_scale.configure(command=self.update_tokens_label)
        
        # Buttons frame
        buttons_frame = ttk.Frame(controls_frame, style='Modern.TFrame')
        buttons_frame.pack(side=tk.RIGHT)
        
        # Generate button
        self.generate_btn = ttk.Button(buttons_frame,
                                      text="🚀 Generate",
                                      style='Modern.TButton',
                                      command=self.generate_response)
        self.generate_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # Clear button
        clear_btn = ttk.Button(buttons_frame,
                              text="🗑️ Clear",
                              command=self.clear_all)
        clear_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # Copy button
        copy_btn = ttk.Button(buttons_frame,
                             text="📋 Copy",
                             command=self.copy_response)
        copy_btn.pack(side=tk.LEFT)

    def clear_placeholder(self, event):
        """Clear placeholder text when focused"""
        if "Enter your coding prompt here..." in self.input_text.get('1.0', tk.END):
            self.input_text.delete('1.0', tk.END)

    def update_temp_label(self, value):
        """Update temperature label"""
        self.temp_label.config(text=f"{float(value):.2f}")

    def update_tokens_label(self, value):
        """Update tokens label"""
        self.tokens_label.config(text=f"{int(float(value))}")

    def check_server_status(self):
        """Check if the server is running"""
        def check():
            try:
                response = requests.get(f"{self.api_base}/llm/health", timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    self.root.after(0, self.update_status, True, data)
                else:
                    self.root.after(0, self.update_status, False, None)
            except:
                self.root.after(0, self.update_status, False, None)

        threading.Thread(target=check, daemon=True).start()

    def update_status(self, is_healthy, data):
        """Update status display"""
        if is_healthy and data:
            self.status_label.config(text="✅ Server Online - Ready for AI Generation")
            model_name = data.get('model_path', '').split('/')[-1]
            self.model_label.config(text=f"Model: {model_name}")
        else:
            self.status_label.config(text="❌ Server Offline - Please start the backend")
            self.model_label.config(text="Model: Not Available")

    def generate_response(self):
        """Generate AI response"""
        prompt = self.input_text.get('1.0', tk.END).strip()

        if not prompt or "Enter your coding prompt here..." in prompt:
            messagebox.showwarning("Warning", "Please enter a prompt first!")
            return

        # Disable button during generation
        self.generate_btn.config(state='disabled', text='🔄 Generating...')

        # Clear previous output
        self.output_text.config(state=tk.NORMAL)
        self.output_text.delete('1.0', tk.END)
        self.output_text.insert('1.0', "🤖 AI is thinking... This may take 30-60 seconds for complex requests.\n\n")
        self.output_text.config(state=tk.DISABLED)

        # Generate in background thread
        def generate():
            try:
                payload = {
                    "prompt": prompt,
                    "temperature": self.temp_var.get(),
                    "max_tokens": self.tokens_var.get(),
                    "top_p": 0.95,
                    "top_k": 50,
                    "repeat_penalty": 1.05
                }

                start_time = time.time()
                response = requests.post(f"{self.api_base}/llm/generate",
                                       json=payload,
                                       timeout=300)  # 5 minute timeout

                if response.status_code == 200:
                    data = response.json()
                    result = data.get('result', 'No response generated')
                    processing_time = data.get('processing_time', time.time() - start_time)
                    tokens_used = data.get('tokens_used', 0)

                    # Format response with metadata
                    formatted_response = f"""🌟 Code Morningstar AI Response
Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Processing Time: {processing_time:.2f}s
Tokens Used: {tokens_used}
Temperature: {self.temp_var.get():.2f}
Max Tokens: {self.tokens_var.get()}

{'='*60}

{result}

{'='*60}
✨ Response generated with CodeLlama 34B Q4_K_M - Maximum Intelligence Mode"""

                    self.root.after(0, self.display_response, formatted_response, True)
                else:
                    error_msg = f"❌ Error {response.status_code}: {response.text}"
                    self.root.after(0, self.display_response, error_msg, False)

            except requests.exceptions.Timeout:
                error_msg = "⏰ Request timed out. The AI might be processing a very complex request."
                self.root.after(0, self.display_response, error_msg, False)
            except Exception as e:
                error_msg = f"❌ Error: {str(e)}\n\nMake sure the backend server is running!"
                self.root.after(0, self.display_response, error_msg, False)

        threading.Thread(target=generate, daemon=True).start()

    def display_response(self, response, success):
        """Display the AI response"""
        self.output_text.config(state=tk.NORMAL)
        self.output_text.delete('1.0', tk.END)
        self.output_text.insert('1.0', response)
        self.output_text.config(state=tk.DISABLED)

        # Re-enable button
        self.generate_btn.config(state='normal', text='🚀 Generate')

        if success:
            self.status_label.config(text="✅ Response Generated Successfully!")
        else:
            self.status_label.config(text="❌ Generation Failed")

    def clear_all(self):
        """Clear all text areas"""
        self.input_text.delete('1.0', tk.END)
        self.output_text.config(state=tk.NORMAL)
        self.output_text.delete('1.0', tk.END)
        self.output_text.config(state=tk.DISABLED)

    def copy_response(self):
        """Copy response to clipboard"""
        response = self.output_text.get('1.0', tk.END).strip()
        if response:
            self.root.clipboard_clear()
            self.root.clipboard_append(response)
            messagebox.showinfo("Success", "Response copied to clipboard!")
        else:
            messagebox.showwarning("Warning", "No response to copy!")

def main():
    """Main function"""
    root = tk.Tk()
    app = CodeMorningstarApp(root)

    # Center window on screen
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")

    root.mainloop()

if __name__ == "__main__":
    main()
