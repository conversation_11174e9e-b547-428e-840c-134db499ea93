from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.api_router import api_router

def get_application() -> FastAPI:
    app = FastAPI(
        title="Code Morningstar API",
        description="Enterprise-grade LLM API with multi-database support and local CodeLlama integration",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    app.include_router(api_router)
    
    @app.get("/")
    def root():
        return {
            "message": "Code Morningstar API",
            "version": "1.0.0",
            "status": "operational",
            "docs": "/docs",
            "health": "/llm/health"
        }
    
    return app

app = get_application()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
