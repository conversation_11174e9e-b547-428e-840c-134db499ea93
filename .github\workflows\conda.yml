name: Conda Environment Test

on:
  push:
    paths:
      - 'environment.yml'
      - '.github/workflows/conda.yml'
  pull_request:
    paths:
      - 'environment.yml'
      - '.github/workflows/conda.yml'

jobs:
  test-conda-env:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: conda-incubator/setup-miniconda@v3
        with:
          environment-file: environment.yml
          activate-environment: code-morningstar
          python-version: 3.11
          auto-activate-base: false
      - name: Test conda environment
        shell: bash -el {0}
        run: |
          conda info
          conda list
          python --version
          # Test core imports
          python -c "import sys; print(f'Python {sys.version}')"
          python -c "import fastapi; print('✅ FastAPI imported successfully')"
          python -c "import pydantic; print('✅ Pydantic imported successfully')"
          python -c "import uvicorn; print('✅ Uvicorn imported successfully')"
          echo "🎉 Conda environment test completed successfully!"