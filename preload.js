const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  // Model management
  getModels: () => ipcRenderer.invoke('get-models'),
  chatCompletion: (payload) => ipcRenderer.invoke('chat-completion', payload),

  // Streaming
  onStreamData: (callback) => ipcRenderer.on('chat-stream-data', callback),
  onStreamEnd: (callback) => ipcRenderer.on('chat-stream-end', callback),
  removeStreamListeners: () => {
    ipcRenderer.removeAllListeners('chat-stream-data');
    ipcRenderer.removeAllListeners('chat-stream-end');
  },

  // System prompts
  getSystemPrompts: () => ipcRenderer.invoke('get-system-prompts'),
  saveSystemPrompt: (category, name, prompt) => ipcRenderer.invoke('save-system-prompt', category, name, prompt),
  deleteSystemPrompt: (category, name) => ipcRenderer.invoke('delete-system-prompt', category, name),

  // Settings
  getSettings: (key) => ipcRenderer.invoke('get-settings', key),
  saveSettings: (key, value) => ipcRenderer.invoke('save-settings', key, value),

  // Menu events
  onNewChat: (callback) => ipcRenderer.on('new-chat', callback),
  onSaveChat: (callback) => ipcRenderer.on('save-chat', callback),
  onToggleSidebar: (callback) => ipcRenderer.on('toggle-sidebar', callback),
  onPromptsUpdated: (callback) => ipcRenderer.on('prompts-updated', callback)
});
