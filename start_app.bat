@echo off
echo ======================================
echo 🌟 Starting Code Morningstar
echo ======================================


echo Cleaning up port 8000 (if needed)...
powershell -Command "try { Get-Process -Id (Get-NetTCPConnection -LocalPort 8000).OwningProcess | Stop-Process -Force } catch {}"

echo Starting Backend Server...
if not exist "%~dp0backend\main.py" (
    echo ERROR: backend/main.py not found!
    pause
    exit /b 1
)
pushd "%~dp0backend"
start "Code Morningstar Backend" python main.py
popd

echo Waiting for backend to start...
timeout /t 3 /nobreak > nul

echo Opening Chat UI in browser...
powershell -Command "Start-Process 'http://localhost:8000/chat-ui.html'"

echo ======================================
echo ✅ Code Morningstar is running!
echo Backend: http://localhost:8000
echo Frontend: windows-ui.html (opened)
echo ========================Get-Process -Id (Get-NetTCPConnection -LocalPort 8000).OwningProcess | Stop-Process
==============
pause
