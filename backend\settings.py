from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # Ultra-high quality model for maximum intelligence
    llm_model_path: str = "models/codellama-34b-instruct.Q4_K_M.gguf"
    host: str = "0.0.0.0"
    port: int = 8000
    max_tokens: int = 4096  # Increased for more detailed responses
    temperature: float = 0.1  # Lower for maximum accuracy
    context_length: int = 8192  # Doubled for better understanding
    # Maximum performance settings for RTX 4070
    n_gpu_layers: int = 50  # Maximum GPU utilization
    n_ctx: int = 8192  # Extended context window
    n_batch: int = 1024  # Larger batch size
    use_mmap: bool = True
    use_mlock: bool = False
    verbose: bool = True
    # Advanced intelligence settings
    top_p: float = 0.95  # High precision sampling
    top_k: int = 50  # Focused vocabulary
    repeat_penalty: float = 1.05  # Minimal repetition
    debug_mode: bool = False
    mongodb_url: str = "mongodb://localhost:27017"
    postgres_url: str = "postgresql://localhost:5432/codemorningstar"
    redis_url: str = "redis://localhost:6379"
    elasticsearch_url: str = "http://localhost:9200"
    enable_cors: bool = True
    enable_database_services: bool = False
    enable_caching: bool = True
    debug_mode: bool = False
    log_level: str = "INFO"
    api_key_header: str = "X-API-Key"
    allowed_origins: list = ["*"]
    
    class Config:
        env_file = ".env"
        case_sensitive = False

_settings: Optional[Settings] = None

def get_settings() -> Settings:
    global _settings
    if _settings is None:
        _settings = Settings()
    return _settings
