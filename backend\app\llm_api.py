from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from backend.services.llm_service import LLMService
from backend.settings import Settings

llm_router = APIRouter()

class LLMRequest(BaseModel):
    prompt: str = Field(..., min_length=1, max_length=4096)

def get_llm_service() -> LLMService:
    return LLMService(str(Settings().llm_model_path))

@llm_router.post("/generate")
def generate_text(request: LLMRequest, llm: LLMService = Depends(get_llm_service)):
    try:
        result = llm.generate(request.prompt)
        return {"result": result}
    except Exception as ex:
        raise HTTPException(status_code=500, detail=str(ex))

@llm_router.get("/health")
def health_check(llm: LLMService = Depends(get_llm_service)):
    """Health check endpoint for LLM service"""
    try:
        model_info = llm.get_model_info()
        return {
            "status": "healthy",
            "model_loaded": model_info["model_loaded"],
            "model_path": model_info["model_path"],
            "model_exists": model_info["model_exists"],
            "model_size_gb": model_info["model_size_gb"]
        }
    except Exception as ex:
        raise HTTPException(status_code=500, detail=f"Health check failed: {str(ex)}")
