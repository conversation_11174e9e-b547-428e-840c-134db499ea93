const { app, B<PERSON>erWindow, ipc<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ray, dialog } = require('electron');
const path = require('path');
const fetch = require('node-fetch');
const Store = require('electron-store');

let mainWindow;
let tray;
const store = new Store();

// Jan.ai configuration
const JAN_BASE_URL = process.env.JAN_BASE_URL || 'http://127.0.0.1:1337/v1';
const JAN_API_KEY = process.env.JAN_API_KEY || '';

// Default system prompts
const DEFAULT_SYSTEM_PROMPTS = {
  'Coding Assistant': {
    'General Coding': 'You are an expert software developer. Provide clean, efficient, and well-documented code solutions. Explain your reasoning and suggest best practices.',
    'Code Review': 'You are a senior code reviewer. Analyze the provided code for bugs, performance issues, security vulnerabilities, and adherence to best practices. Provide constructive feedback.',
    'Debugging Helper': 'You are a debugging expert. Help identify and fix bugs in code. Provide step-by-step debugging strategies and explain the root cause of issues.',
    'Architecture Advisor': 'You are a software architect. Help design scalable, maintainable software systems. Consider performance, security, and future extensibility.'
  },
  'Creative Writing': {
    'Story Writer': 'You are a creative storyteller. Write engaging, imaginative stories with rich characters and compelling plots.',
    'Technical Writer': 'You are a technical writing expert. Create clear, concise documentation that is easy to understand and follow.',
    'Content Creator': 'You are a content creation specialist. Generate engaging, informative content tailored to the target audience.',
    'Editor': 'You are a professional editor. Improve writing clarity, flow, grammar, and style while preserving the author\'s voice.'
  },
  'Analysis & Research': {
    'Data Analyst': 'You are a data analysis expert. Interpret data, identify patterns, and provide actionable insights with clear explanations.',
    'Research Assistant': 'You are a research specialist. Provide thorough, well-sourced information and help synthesize complex topics.',
    'Business Analyst': 'You are a business analysis expert. Evaluate business problems and provide strategic recommendations.',
    'Academic Researcher': 'You are an academic researcher. Provide scholarly analysis with proper methodology and evidence-based conclusions.'
  },
  'General Purpose': {
    'Helpful Assistant': 'You are a helpful, harmless, and honest AI assistant. Provide accurate information and assist with a wide variety of tasks.',
    'Conversational': 'You are a friendly conversational partner. Engage in natural, helpful dialogue while being informative and supportive.',
    'Problem Solver': 'You are a problem-solving expert. Break down complex problems into manageable steps and provide practical solutions.',
    'Tutor': 'You are an expert tutor. Explain concepts clearly, provide examples, and adapt your teaching style to help the user learn effectively.'
  }
};

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1000,
    minHeight: 700,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    titleBarStyle: 'hiddenInset',
    show: false,
    icon: path.join(__dirname, 'assets', 'icon.png'),
    backgroundColor: '#0b0f14'
  });

  mainWindow.loadFile('renderer.html');
  
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // Initialize default system prompts if not exists
    if (!store.get('systemPrompts')) {
      store.set('systemPrompts', DEFAULT_SYSTEM_PROMPTS);
    }
  });

  // Create system tray
  createTray();
  
  // Create menu
  createMenu();
}

function createTray() {
  try {
    tray = new Tray(path.join(__dirname, 'assets', 'icon.png'));
    const contextMenu = Menu.buildFromTemplate([
      { label: 'Show', click: () => mainWindow.show() },
      { label: 'Hide', click: () => mainWindow.hide() },
      { type: 'separator' },
      { label: 'Quit', click: () => app.quit() }
    ]);
    tray.setContextMenu(contextMenu);
    tray.setToolTip('Jan Advanced Desktop');
  } catch (error) {
    console.log('Tray creation failed:', error);
  }
}

function createMenu() {
  const template = [
    {
      label: 'File',
      submenu: [
        { label: 'New Chat', accelerator: 'CmdOrCtrl+N', click: () => mainWindow.webContents.send('new-chat') },
        { label: 'Save Chat', accelerator: 'CmdOrCtrl+S', click: () => mainWindow.webContents.send('save-chat') },
        { type: 'separator' },
        { label: 'Import Prompts', click: () => importPrompts() },
        { label: 'Export Prompts', click: () => exportPrompts() },
        { type: 'separator' },
        { role: 'quit' }
      ]
    },
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' },
        { role: 'selectall' }
      ]
    },
    {
      label: 'View',
      submenu: [
        { label: 'Toggle Sidebar', accelerator: 'CmdOrCtrl+B', click: () => mainWindow.webContents.send('toggle-sidebar') },
        { label: 'Toggle Developer Tools', accelerator: 'F12', click: () => mainWindow.webContents.toggleDevTools() },
        { type: 'separator' },
        { role: 'reload' },
        { role: 'forceReload' },
        { type: 'separator' },
        { role: 'resetzoom' },
        { role: 'zoomin' },
        { role: 'zoomout' }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

async function importPrompts() {
  const result = await dialog.showOpenDialog(mainWindow, {
    properties: ['openFile'],
    filters: [{ name: 'JSON Files', extensions: ['json'] }]
  });
  
  if (!result.canceled) {
    try {
      const data = JSON.parse(fs.readFileSync(result.filePaths[0], 'utf8'));
      store.set('systemPrompts', { ...store.get('systemPrompts'), ...data });
      mainWindow.webContents.send('prompts-updated');
    } catch (error) {
      console.error('Import failed:', error);
    }
  }
}

async function exportPrompts() {
  const result = await dialog.showSaveDialog(mainWindow, {
    filters: [{ name: 'JSON Files', extensions: ['json'] }],
    defaultPath: 'system-prompts.json'
  });
  
  if (!result.canceled) {
    try {
      fs.writeFileSync(result.filePath, JSON.stringify(store.get('systemPrompts'), null, 2));
    } catch (error) {
      console.error('Export failed:', error);
    }
  }
}

// IPC handlers for enhanced functionality
ipcMain.handle('get-models', async () => {
  try {
    const response = await fetch(`${JAN_BASE_URL}/models`, {
      headers: {
        'Authorization': JAN_API_KEY ? `Bearer ${JAN_API_KEY}` : undefined,
        'Content-Type': 'application/json'
      }
    });
    const data = await response.json();
    return { success: true, data };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('chat-completion', async (event, payload) => {
  try {
    const response = await fetch(`${JAN_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': JAN_API_KEY ? `Bearer ${JAN_API_KEY}` : undefined,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload)
    });

    if (payload.stream) {
      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data:')) {
            const data = line.replace(/^data:\s*/, '').trim();
            if (data === '[DONE]') {
              event.sender.send('chat-stream-end');
              return;
            }
            try {
              const json = JSON.parse(data);
              event.sender.send('chat-stream-data', json);
            } catch (e) {
              // Ignore parsing errors
            }
          }
        }
      }
    } else {
      const data = await response.json();
      return { success: true, data };
    }
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// System prompt management
ipcMain.handle('get-system-prompts', () => {
  return store.get('systemPrompts', DEFAULT_SYSTEM_PROMPTS);
});

ipcMain.handle('save-system-prompt', (event, category, name, prompt) => {
  const prompts = store.get('systemPrompts', {});
  if (!prompts[category]) prompts[category] = {};
  prompts[category][name] = prompt;
  store.set('systemPrompts', prompts);
  return true;
});

ipcMain.handle('delete-system-prompt', (event, category, name) => {
  const prompts = store.get('systemPrompts', {});
  if (prompts[category] && prompts[category][name]) {
    delete prompts[category][name];
    if (Object.keys(prompts[category]).length === 0) {
      delete prompts[category];
    }
    store.set('systemPrompts', prompts);
  }
  return true;
});

// Settings management
ipcMain.handle('get-settings', (event, key) => {
  return store.get(key);
});

ipcMain.handle('save-settings', (event, key, value) => {
  store.set(key, value);
  return true;
});

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});