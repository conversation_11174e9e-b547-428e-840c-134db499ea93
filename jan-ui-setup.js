#!/usr/bin/env node
// ─────────────────────────────────────────────────────────────────────────────
// ChatGPT‑Style UI for Jan (Code‑Morningstar) - Setup Script
// One‑shot setup: Creates a tiny Node proxy + a zero‑build React UI (CDN) with streaming.
// - Proxy (server.js) keeps your JAN_API_KEY out of the browser and handles CORS.
// - U<PERSON> (index.html) talks to the proxy; looks/feels like ChatGP<PERSON>; code‑friendly.
// Ports: Proxy 5175 (configurable). <PERSON> stays on 127.0.0.1:1337. Tower stays 8000.
// ─────────────────────────────────────────────────────────────────────────────

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { execSync } from 'child_process';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// File templates
const SERVER_JS = `import express from "express";
import cors from "cors";
import fetch from "node-fetch";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const JAN_BASE_URL = process.env.JAN_BASE_URL || "http://127.0.0.1:1337/v1";
const JAN_API_KEY  = process.env.JAN_API_KEY  || ""; // if Jan is open, can be blank
const UI_PORT      = Number(process.env.UI_PORT || 5175);

const app = express();
app.use(cors());
app.use(express.json({ limit: "2mb" }));

// Serve static UI
app.use(express.static(path.join(__dirname, "public")));

// Health
app.get("/health", (_, res) => res.json({ ok: true, jan: JAN_BASE_URL }));

// List models (pass through to Jan)
app.get("/v1/models", async (req, res) => {
  const r = await fetch(\`\${JAN_BASE_URL}/models\`, {
    headers: {
      "Authorization": JAN_API_KEY ? \`Bearer \${JAN_API_KEY}\` : undefined,
      "Content-Type": "application/json"
    }
  });
  res.status(r.status);
  r.body.pipe(res);
});

// Chat completions (streaming or non‑streaming). We stream if client sets stream=true
app.post("/v1/chat/completions", async (req, res) => {
  const body = req.body || {};
  const stream = Boolean(body.stream);
  const upstream = await fetch(\`\${JAN_BASE_URL}/chat/completions\`, {
    method: "POST",
    headers: {
      "Authorization": JAN_API_KEY ? \`Bearer \${JAN_API_KEY}\` : undefined,
      "Content-Type": "application/json"
    },
    body: JSON.stringify(body)
  });

  // Pass status and headers
  res.status(upstream.status);
  if (stream) {
    res.setHeader("Content-Type", "text/event-stream");
    res.setHeader("Cache-Control", "no-cache");
    res.setHeader("Connection", "keep-alive");
  } else {
    res.setHeader("Content-Type", "application/json");
  }

  upstream.body.pipe(res);
});

app.listen(UI_PORT, () => {
  console.log(\`UI + Proxy on http://127.0.0.1:\${UI_PORT}\`);
  console.log(\`Proxying Jan at \${JAN_BASE_URL}\`);
});`;

const PACKAGE_JSON = `{
  "name": "jan-ui-chatgpt-style",
  "version": "1.0.0",
  "description": "ChatGPT-style UI for Jan.ai with streaming support",
  "type": "module",
  "main": "server.js",
  "scripts": {
    "start": "node server.js",
    "dev": "node server.js"
  },
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "node-fetch": "^3.3.2"
  },
  "keywords": ["jan", "chatgpt", "ui", "ai", "streaming"],
  "author": "Code-Morningstar",
  "license": "MIT"
}`;

const INDEX_HTML = `<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Chat — Local Jan (Code‑Ready)</title>
  <!-- Tailwind (CDN) for rapid styling) -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- React + ReactDOM (CDN) -->
  <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
  <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
  <!-- Highlight.js for code blocks -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github-dark.min.css"/>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
  <style>
    :root { color-scheme: dark; }
    body { background: #0b0f14; }
    .msg { white-space: pre-wrap; }
    .bubble { border-radius: 1rem; }
    .scrollbars::-webkit-scrollbar { width: 10px; }
    .scrollbars::-webkit-scrollbar-thumb { background: #1f2937; border-radius: 8px; }
    .prose code { white-space: pre-wrap; }
    .codeblock { background: #0b1220; border: 1px solid #1f2a37; border-radius: 12px; padding: 12px; }
  </style>
</head>
<body class="h-screen text-gray-100">
  <div id="root" class="h-full"></div>

  <script type="module">
    const { useState, useEffect, useRef } = React;
    const API_BASE = location.origin; // proxy serves same origin

    function Toolbar({ models, currentModel, setModel, temp, setTemp, onClear }) {
      return (
        React.createElement('div', { className: 'flex items-center gap-3 p-3 border-b border-gray-800 bg-[#0f1520]' },
          React.createElement('div', { className: 'text-lg font-semibold' }, 'Chat (Local Jan)'),
          React.createElement('select', {
            className: 'bg-gray-900 border border-gray-700 rounded-md px-2 py-1',
            value: currentModel,
            onChange: e => setModel(e.target.value)
          }, models.map(m => React.createElement('option', { key: m, value: m }, m))),
          React.createElement('div', { className: 'flex items-center gap-2 ml-auto' },
            React.createElement('label', { className: 'text-sm text-gray-400' }, \`temp: \${temp.toFixed(2)}\`),
            React.createElement('input', {
              type: 'range', min: 0, max: 1, step: 0.05, value: temp,
              onChange: e => setTemp(parseFloat(e.target.value))
            }),
            React.createElement('button', {
              className: 'px-3 py-1 rounded-md bg-gray-800 hover:bg-gray-700 border border-gray-700',
              onClick: onClear
            }, 'Clear')
          )
        )
      );
    }

    function Message({ role, content }) {
      // render code fences
      const containerRef = useRef(null);
      useEffect(() => {
        if (containerRef.current) {
          containerRef.current.querySelectorAll('pre code').forEach(block => hljs.highlightElement(block));
        }
      }, [content]);

      const isUser = role === 'user';
      return (
        React.createElement('div', { className: \`flex \${isUser ? 'justify-end' : 'justify-start'} w-full\` },
          React.createElement('div', {
            className: \`bubble max-w-3xl \${isUser ? 'bg-blue-600/20 border-blue-500/30' : 'bg-gray-800/60 border-gray-700'} border p-3 my-2\` },
            React.createElement('div', { ref: containerRef, className: 'prose prose-invert prose-pre:my-2 prose-code:px-1 prose-code:py-0.5' },
              renderMarkdown(content)
            )
          )
        )
      );
    }

    function renderMarkdown(text) {
      // Simple fenced code parsing: \`\`\`lang\\ncode\\n\`\`\`
      const parts = [];
      const regex = /\`\`\`(\\w+)?\\n([\\s\\S]*?)\`\`\`/g;
      let lastIndex = 0, m;
      while ((m = regex.exec(text)) !== null) {
        if (m.index > lastIndex) {
          parts.push(React.createElement('div', { key: \`t-\${lastIndex}\` }, text.slice(lastIndex, m.index)));
        }
        const lang = m[1] || 'plaintext';
        const code = m[2];
        parts.push(
          React.createElement('div', { className: 'codeblock my-2', key: \`c-\${m.index}\` },
            React.createElement('div', { className: 'flex justify-between items-center mb-2 text-xs text-gray-400' },
              React.createElement('span', null, lang),
              React.createElement('button', {
                className: 'px-2 py-0.5 border border-gray-600 rounded hover:bg-gray-700',
                onClick: () => navigator.clipboard.writeText(code)
              }, 'Copy')
            ),
            React.createElement('pre', null,
              React.createElement('code', { className: \`language-\${lang}\` }, code)
            )
          )
        );
        lastIndex = regex.lastIndex;
      }
      if (lastIndex < text.length) {
        parts.push(React.createElement('div', { key: \`t-end\` }, text.slice(lastIndex)));
      }
      return parts;
    }

    function App() {
      const [models, setModels] = useState([]);
      const [model, setModel] = useState('');
      const [temp, setTemp] = useState(0.2);
      const [messages, setMessages] = useState([]);
      const [input, setInput] = useState('');
      const [streaming, setStreaming] = useState(false);
      const abortRef = useRef(null);
      const scrollRef = useRef(null);

      useEffect(() => {
        fetch(\`\${API_BASE}/v1/models\`).then(r => r.json()).then(d => {
          const ids = (d?.data || d?.models || []).map(m => m.id || m);
          setModels(ids);
          if (!model && ids.length) setModel(ids[0]);
        }).catch(console.error);
      }, []);

      useEffect(() => {
        if (scrollRef.current) {
          scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
        }
      }, [messages, streaming]);

      async function send() {
        if (!input.trim() || !model) return;
        const userMsg = { role: 'user', content: input };
        const draftAssistant = { role: 'assistant', content: '' };
        setMessages(prev => [...prev, userMsg, draftAssistant]);
        setInput('');
        setStreaming(true);

        const ctrl = new AbortController();
        abortRef.current = ctrl;
        try {
          const res = await fetch(\`\${API_BASE}/v1/chat/completions\`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              model,
              messages: [...messages, userMsg].map(m => ({ role: m.role, content: m.content })),
              temperature: temp,
              stream: true
            }),
            signal: ctrl.signal
          });
          if (!res.ok) {
            const text = await res.text();
            throw new Error(text || \`HTTP \${res.status}\`);
          }
          const reader = res.body.getReader();
          const decoder = new TextDecoder();
          let assistantText = '';

          while (true) {
            const { value, done } = await reader.read();
            if (done) break;
            const chunk = decoder.decode(value, { stream: true });
            const lines = chunk.split('\\n');
            for (const line of lines) {
              if (!line.startsWith('data:')) continue;
              const payload = line.replace(/^data:\\s*/, '').trim();
              if (payload === '[DONE]') break;
              try {
                const json = JSON.parse(payload);
                const delta = json?.choices?.[0]?.delta?.content || '';
                if (delta) {
                  assistantText += delta;
                  setMessages(prev => {
                    const copy = [...prev];
                    copy[copy.length - 1] = { role: 'assistant', content: assistantText };
                    return copy;
                  });
                }
              } catch {}
            }
          }
        } catch (err) {
          setMessages(prev => {
            const copy = [...prev];
            copy[copy.length - 1] = { role: 'assistant', content: \`Error: \${err.message}\` };
            return copy;
          });
        } finally {
          setStreaming(false);
          abortRef.current = null;
        }
      }

      function stop() {
        if (abortRef.current) abortRef.current.abort();
      }

      function onKey(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
          e.preventDefault();
          send();
        }
      }

      function clearChat() { setMessages([]); }

      return (
        React.createElement('div', { className: 'h-full grid grid-rows-[auto,1fr,auto]' },
          React.createElement(Toolbar, { models, currentModel: model, setModel, temp, setTemp, onClear: clearChat }),
          React.createElement('div', { ref: scrollRef, className: 'scrollbars overflow-y-auto p-4 space-y-2' },
            messages.map((m, i) => React.createElement(Message, { key: i, role: m.role, content: m.content }))
          ),
          React.createElement('div', { className: 'p-3 border-t border-gray-800 bg-[#0f1520]' },
            React.createElement('div', { className: 'max-w-4xl mx-auto flex items-end gap-2' },
              React.createElement('textarea', {
                value: input,
                onChange: e => setInput(e.target.value),
                onKeyDown: onKey,
                rows: 2,
                placeholder: 'Ask or paste code…',
                className: 'flex-1 resize-none bg-gray-900 border border-gray-700 rounded-xl p-3 focus:outline-none focus:ring-2 focus:ring-blue-600'
              }),
              streaming ? (
                React.createElement('button', { onClick: stop, className: 'px-4 py-2 rounded-lg bg-red-600 hover:bg-red-700' }, 'Stop')
              ) : (
                React.createElement('button', { onClick: send, className: 'px-4 py-2 rounded-lg bg-blue-600 hover:bg-blue-700' }, 'Send')
              )
            )
          )
        )
      );
    }

    ReactDOM.createRoot(document.getElementById('root')).render(React.createElement(App));
  </script>
</body>
</html>`;

// Setup functions
function createDirectory(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`✓ Created directory: ${dirPath}`);
  }
}

function writeFile(filePath, content) {
  fs.writeFileSync(filePath, content, 'utf8');
  console.log(`✓ Created file: ${filePath}`);
}

function installDependencies() {
  try {
    console.log('📦 Installing dependencies...');
    execSync('npm install', { stdio: 'inherit' });
    console.log('✓ Dependencies installed successfully');
  } catch (error) {
    console.error('❌ Failed to install dependencies:', error.message);
    console.log('💡 You can install manually with: npm install');
  }
}

const README_MD = `# ChatGPT-Style UI for Jan (Code-Morningstar)

A lightweight, streaming-enabled ChatGPT-style interface for Jan.ai with code syntax highlighting.

## Features

- 🚀 Zero-build React UI (CDN-based)
- 📡 Real-time streaming responses
- 🎨 ChatGPT-like dark theme
- 💻 Code syntax highlighting
- 🔒 Secure proxy (API key never touches browser)
- ⚡ Fast setup and deployment

## Quick Start

1. **Set environment variables:**
   \`\`\`bash
   setx JAN_BASE_URL "http://127.0.0.1:1337/v1"
   setx JAN_API_KEY "<YOUR_32+_CHAR_KEY>"
   setx UI_PORT "5175"
   \`\`\`

2. **Install and run:**
   \`\`\`bash
   npm install
   npm start
   \`\`\`

3. **Open in browser:**
   http://127.0.0.1:5175

## Architecture

- **server.js**: Node.js proxy server (handles CORS, API key security)
- **public/index.html**: React UI with streaming support
- **Ports**: UI on 5175, Jan on 1337, Tower on 8000

## Security Notes

- API key never reaches the browser
- Proxy handles all Jan.ai communication
- Keep Jan bound to 127.0.0.1 for security
`;

// Main setup function
function setup() {
  console.log('🚀 Setting up ChatGPT-style UI for Jan...\n');

  // Create package.json
  writeFile('package.json', PACKAGE_JSON);

  // Create server.js
  writeFile('server.js', SERVER_JS);

  // Create public directory and index.html
  createDirectory('public');
  writeFile('public/index.html', INDEX_HTML);

  // Create README
  writeFile('README.md', README_MD);

  console.log('\n✅ Setup complete! Files created:');
  console.log('   📦 package.json');
  console.log('   🖥️  server.js');
  console.log('   📁 public/');
  console.log('   🌐 public/index.html');
  console.log('   📖 README.md');

  console.log('\n📋 Next steps:');
  console.log('1. Set environment variables:');
  console.log('   setx JAN_BASE_URL "http://127.0.0.1:1337/v1"');
  console.log('   setx JAN_API_KEY "<YOUR_32+_CHAR_KEY>"');
  console.log('   setx UI_PORT "5175"');
  console.log('2. Install dependencies: npm install');
  console.log('3. Run the server: npm start');
  console.log('4. Open http://127.0.0.1:5175');

  // Ask if user wants to install dependencies now
  console.log('\n❓ Install dependencies now? (y/n)');
}

// Run setup
setup();
