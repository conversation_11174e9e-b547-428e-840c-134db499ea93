{"name": "jan-ui-chatgpt-style", "version": "1.0.0", "description": "ChatGPT-style UI for Jan.ai with streaming support", "type": "module", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "node-fetch": "^3.3.2"}, "keywords": ["jan", "chatgpt", "ui", "ai", "streaming"], "author": "Code-Morningstar", "license": "MIT"}