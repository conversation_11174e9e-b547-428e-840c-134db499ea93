{"name": "jan-advanced-desktop", "version": "1.0.0", "description": "Advanced ChatGPT-style desktop app for Jan.ai with full parameter controls", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux"}, "dependencies": {"electron": "^27.0.0", "node-fetch": "^3.3.2", "electron-store": "^8.1.0"}, "devDependencies": {"electron-builder": "^24.6.4"}, "build": {"appId": "com.codemorningstar.jan-advanced-desktop", "productName": "Jan Advanced Desktop", "directories": {"output": "dist"}, "files": ["main.js", "preload.js", "renderer.html", "assets/**/*", "package.json"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}}, "keywords": ["jan", "chatgpt", "desktop", "ai", "electron", "advanced"], "author": "Code-Morningstar", "license": "MIT"}