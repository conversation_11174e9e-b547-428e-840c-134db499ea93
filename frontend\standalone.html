<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌟 Code Morningstar</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        header {
            text-align: center;
            margin-bottom: 3rem;
        }

        h1 {
            font-size: 3rem;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            margin-bottom: 1rem;
        }

        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 2rem;
        }

        .status {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: rgba(251, 191, 36, 0.2);
            border: 1px solid rgba(251, 191, 36, 0.3);
            border-radius: 20px;
            font-size: 0.9rem;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #fbbf24;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .main-panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 2rem;
        }

        .prompt-area {
            margin-bottom: 1rem;
        }

        #prompt {
            width: 100%;
            padding: 1rem;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1rem;
            font-family: inherit;
            resize: vertical;
            min-height: 120px;
        }

        #prompt:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.5);
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
        }

        #prompt::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .controls {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            flex-wrap: wrap;
        }

        button {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .secondary:hover:not(:disabled) {
            background: rgba(255, 255, 255, 0.2);
        }

        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .response-panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 2rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: none;
        }

        .response-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .response-content {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .response-content pre {
            margin: 0;
            white-space: pre-wrap;
            word-wrap: break-word;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 0.95rem;
            line-height: 1.5;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 2rem;
        }

        .spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-bottom: 1rem;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .examples {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }

        .example-card {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.2s;
        }

        .example-card:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .example-title {
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #fbbf24;
        }

        .example-preview {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
            }
            
            .response-header {
                flex-direction: column;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>🌟 Code Morningstar</h1>
            <p class="subtitle">Local LLM Powered Assistant</p>
            <div class="status">
                <span class="status-dot"></span>
                <span id="status-text">Checking...</span>
            </div>
        </header>

        <div class="main-panel">
            <div class="prompt-area">
                <textarea 
                    id="prompt" 
                    placeholder="Enter your prompt here... (Ctrl+Enter to send)"
                ></textarea>
            </div>
            <div class="controls">
                <button onclick="clearPrompt()" class="secondary">
                    🗑️ Clear
                </button>
                <button onclick="generateResponse()" id="generate-btn" class="primary">
                    🚀 Generate
                </button>
            </div>
        </div>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Generating response...</p>
        </div>

        <div class="response-panel" id="response-panel">
            <div class="response-header">
                <h2>💡 Response</h2>
                <button onclick="copyResponse()" class="secondary">
                    📋 Copy
                </button>
            </div>
            <div class="response-content">
                <pre id="response-text"></pre>
            </div>
        </div>

        <div class="examples">
            <div class="example-card" onclick="loadExample('Write a Python function that calculates the factorial of a number using recursion.')">
                <div class="example-title">🐍 Python Function</div>
                <div class="example-preview">Write a Python function that calculates factorial...</div>
            </div>
            <div class="example-card" onclick="loadExample('Create a JavaScript async function that fetches data from an API and handles errors gracefully.')">
                <div class="example-title">🌐 JavaScript Async</div>
                <div class="example-preview">Create a JavaScript async function that fetches...</div>
            </div>
            <div class="example-card" onclick="loadExample('Show me how to create a responsive CSS grid layout with 3 columns that becomes 1 column on mobile.')">
                <div class="example-title">🎨 CSS Grid Layout</div>
                <div class="example-preview">Show me how to create a responsive CSS grid...</div>
            </div>
            <div class="example-card" onclick="loadExample('Write a SQL query to find the top 5 customers by total order value in the last 30 days.')">
                <div class="example-title">🔧 SQL Query</div>
                <div class="example-preview">Write a SQL query to find the top 5 customers...</div>
            </div>
        </div>
    </div>

    <script>
        let isLoading = false;

        // Check model status on load
        async function checkStatus() {
            try {
                const response = await fetch('http://localhost:8000/llm/health');
                const data = await response.json();
                const statusText = document.getElementById('status-text');
                statusText.textContent = data.model_loaded ? 'Model Loaded' : 'Model Not Loaded';
            } catch (error) {
                console.error('Failed to check status:', error);
            }
        }

        // Generate response
        async function generateResponse() {
            const prompt = document.getElementById('prompt').value.trim();
            if (!prompt || isLoading) return;

            isLoading = true;
            const generateBtn = document.getElementById('generate-btn');
            const loading = document.getElementById('loading');
            const responsePanel = document.getElementById('response-panel');
            
            generateBtn.disabled = true;
            generateBtn.innerHTML = '🔄 Generating...';
            loading.style.display = 'block';
            responsePanel.style.display = 'none';

            try {
                const response = await fetch('http://localhost:8000/llm/generate', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        prompt: prompt,
                        max_tokens: 256,
                        temperature: 0.7
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();
                document.getElementById('response-text').textContent = data.result;
                responsePanel.style.display = 'block';
                
            } catch (error) {
                document.getElementById('response-text').textContent = `Error: ${error.message}`;
                responsePanel.style.display = 'block';
            } finally {
                isLoading = false;
                generateBtn.disabled = false;
                generateBtn.innerHTML = '🚀 Generate';
                loading.style.display = 'none';
            }
        }

        // Clear prompt
        function clearPrompt() {
            document.getElementById('prompt').value = '';
            document.getElementById('response-panel').style.display = 'none';
        }

        // Load example
        function loadExample(text) {
            document.getElementById('prompt').value = text;
        }

        // Copy response
        function copyResponse() {
            const responseText = document.getElementById('response-text').textContent;
            navigator.clipboard.writeText(responseText).then(() => {
                const btn = event.target;
                const original = btn.textContent;
                btn.textContent = '✅ Copied!';
                setTimeout(() => {
                    btn.textContent = original;
                }, 1000);
            });
        }

        // Keyboard shortcuts
        document.getElementById('prompt').addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                e.preventDefault();
                generateResponse();
            }
        });

        // Initialize
        checkStatus();
    </script>
</body>
</html>
