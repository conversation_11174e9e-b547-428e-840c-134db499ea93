"""
Advanced Code Analysis Service for Maximum Intelligence
Provides deep code understanding and reasoning capabilities.
"""

import ast
import re
from typing import Dict, List, Any, Optional
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class CodeAnalyzer:
    """Advanced code analysis for enhanced LLM responses."""
    
    def __init__(self):
        self.supported_languages = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.java': 'java',
            '.cpp': 'cpp',
            '.c': 'c',
            '.cs': 'csharp',
            '.go': 'go',
            '.rs': 'rust',
            '.php': 'php',
            '.rb': 'ruby'
        }
    
    def analyze_code_context(self, code: str, language: str = 'python') -> Dict[str, Any]:
        """Analyze code to provide rich context for LLM."""
        context = {
            'language': language,
            'complexity': self._calculate_complexity(code),
            'patterns': self._detect_patterns(code),
            'functions': self._extract_functions(code, language),
            'classes': self._extract_classes(code, language),
            'imports': self._extract_imports(code, language),
            'comments': self._extract_comments(code),
            'suggestions': self._generate_suggestions(code, language)
        }
        return context
    
    def _calculate_complexity(self, code: str) -> Dict[str, int]:
        """Calculate code complexity metrics."""
        lines = code.split('\n')
        return {
            'lines_of_code': len([l for l in lines if l.strip() and not l.strip().startswith('#')]),
            'total_lines': len(lines),
            'cyclomatic_complexity': self._cyclomatic_complexity(code),
            'nesting_depth': self._max_nesting_depth(code)
        }
    
    def _detect_patterns(self, code: str) -> List[str]:
        """Detect design patterns and code structures."""
        patterns = []
        
        # Common patterns
        if 'class.*Factory' in code:
            patterns.append('Factory Pattern')
        if 'class.*Singleton' in code:
            patterns.append('Singleton Pattern')
        if 'def __enter__' in code and 'def __exit__' in code:
            patterns.append('Context Manager')
        if '@property' in code:
            patterns.append('Property Decorator')
        if 'yield' in code:
            patterns.append('Generator')
        if 'async def' in code:
            patterns.append('Async/Await')
        
        return patterns
    
    def _extract_functions(self, code: str, language: str) -> List[Dict[str, Any]]:
        """Extract function definitions and metadata."""
        functions = []
        
        if language == 'python':
            try:
                tree = ast.parse(code)
                for node in ast.walk(tree):
                    if isinstance(node, ast.FunctionDef):
                        functions.append({
                            'name': node.name,
                            'args': [arg.arg for arg in node.args.args],
                            'line': node.lineno,
                            'is_async': isinstance(node, ast.AsyncFunctionDef),
                            'decorators': [d.id if hasattr(d, 'id') else str(d) for d in node.decorator_list]
                        })
            except SyntaxError:
                pass
        
        return functions
    
    def _extract_classes(self, code: str, language: str) -> List[Dict[str, Any]]:
        """Extract class definitions and metadata."""
        classes = []
        
        if language == 'python':
            try:
                tree = ast.parse(code)
                for node in ast.walk(tree):
                    if isinstance(node, ast.ClassDef):
                        classes.append({
                            'name': node.name,
                            'bases': [base.id if hasattr(base, 'id') else str(base) for base in node.bases],
                            'line': node.lineno,
                            'methods': [n.name for n in node.body if isinstance(n, ast.FunctionDef)]
                        })
            except SyntaxError:
                pass
        
        return classes
    
    def _extract_imports(self, code: str, language: str) -> List[str]:
        """Extract import statements."""
        imports = []
        
        if language == 'python':
            import_pattern = r'^(?:from\s+\S+\s+)?import\s+.+$'
            imports = re.findall(import_pattern, code, re.MULTILINE)
        
        return imports
    
    def _extract_comments(self, code: str) -> List[str]:
        """Extract comments for context."""
        comment_pattern = r'#.*$|""".*?"""|\'\'\'.*?\'\'\''
        comments = re.findall(comment_pattern, code, re.MULTILINE | re.DOTALL)
        return [c.strip() for c in comments if c.strip()]
    
    def _cyclomatic_complexity(self, code: str) -> int:
        """Calculate cyclomatic complexity."""
        complexity = 1  # Base complexity
        
        # Count decision points
        decision_keywords = ['if', 'elif', 'while', 'for', 'try', 'except', 'and', 'or']
        for keyword in decision_keywords:
            complexity += len(re.findall(rf'\b{keyword}\b', code))
        
        return complexity
    
    def _max_nesting_depth(self, code: str) -> int:
        """Calculate maximum nesting depth."""
        lines = code.split('\n')
        max_depth = 0
        current_depth = 0
        
        for line in lines:
            stripped = line.lstrip()
            if stripped and not stripped.startswith('#'):
                indent = len(line) - len(stripped)
                current_depth = indent // 4  # Assuming 4-space indentation
                max_depth = max(max_depth, current_depth)
        
        return max_depth
    
    def _generate_suggestions(self, code: str, language: str) -> List[str]:
        """Generate intelligent suggestions for code improvement."""
        suggestions = []
        
        # Performance suggestions
        if 'for' in code and 'append' in code:
            suggestions.append("Consider using list comprehension for better performance")
        
        if 'global' in code:
            suggestions.append("Consider avoiding global variables for better code organization")
        
        if len(code.split('\n')) > 50:
            suggestions.append("Consider breaking this into smaller functions")
        
        # Security suggestions
        if 'eval(' in code:
            suggestions.append("Avoid using eval() for security reasons")
        
        if 'exec(' in code:
            suggestions.append("Avoid using exec() for security reasons")
        
        return suggestions

    def enhance_prompt_with_context(self, prompt: str, code_context: Optional[str] = None) -> str:
        """Enhance the prompt with intelligent code context."""
        if not code_context:
            return prompt
        
        analysis = self.analyze_code_context(code_context)
        
        enhanced_prompt = f"""
Code Context Analysis:
- Language: {analysis['language']}
- Complexity: {analysis['complexity']['cyclomatic_complexity']} (cyclomatic)
- Functions: {len(analysis['functions'])}
- Classes: {len(analysis['classes'])}
- Patterns Detected: {', '.join(analysis['patterns']) if analysis['patterns'] else 'None'}

Original Request: {prompt}

Please provide a response that takes into account the code complexity, patterns, and context above.
"""
        return enhanced_prompt
