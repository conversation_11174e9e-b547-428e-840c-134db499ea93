#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

// Launch the Code Morningstar application
console.log('🌟 Starting Code Morningstar...');

// Start backend
const backend = spawn('python', ['backend/start.py'], {
  cwd: __dirname,
  stdio: 'inherit'
});

// Handle backend exit
backend.on('exit', (code) => {
  console.log(`Backend exited with code ${code}`);
  process.exit(code);
});

console.log('✅ Code Morningstar backend starting...');
console.log('🔗 Backend: http://localhost:8000');
console.log('🔗 API Docs: http://localhost:8000/docs');
console.log('🔗 Frontend: Open frontend/standalone.html');